<!-- 成长主页重构版 - 基于V2-A简洁任务导向型 -->
<view class="page-container">
  <!-- 自定义导航栏 -->
  <custom-navbar title="成长" show-user-selector="{{true}}" show-back-button="{{false}}" bind:navbarHeightChange="onNavbarHeightChange" bind:childChange="onChildChange"></custom-navbar>
  <!-- 数据源信息显示 -->
  <view class="data-source-info" style="margin-top: {{navbarHeight + 10}}px;" wx:if="{{dataSourceInfo}}">
    <text class="data-source-text">{{dataSourceInfo}}</text>
  </view>
  <!-- 顶部导航Tab -->
  <view class="tab-nav" style="margin-top: {{dataSourceInfo ? '10' : navbarHeight}}px;">
    <view class="tab-item {{currentTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-tab="0">
      <text class="tab-text">任务</text>
    </view>
    <view class="tab-item {{currentTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-tab="1">
      <text class="tab-text">成长</text>
    </view>
  </view>
  <!-- 任务Tab内容 -->
  <view class="tab-content" wx:if="{{currentTab === 0}}">
    <!-- 训练营列表 -->
    <view class="camps-section">
      <view class="section-title">
        <text class="title-icon">📚</text>
        <text class="title-text">我的训练营</text>
        <text class="camps-count">{{campList.length}}个进行中</text>
      </view>
      <!-- 训练营卡片列表 -->
      <view class="camp-list" wx:if="{{campList.length > 0}}">
        <view class="camp-card" wx:for="{{campList}}" wx:key="id">
          <!-- 训练营基本信息 -->
          <view class="camp-header">
            <view class="camp-info" bindtap="goToCampDetail" data-camp="{{item}}">
              <text class="camp-title">{{item.title}}</text>
              <text class="camp-subtitle">{{item.subtitle}}</text>
            </view>
            <view class="header-right">
              <view class="status-icon">
                <text class="status-symbol completed" wx:if="{{item.todayStatus === 'completed'}}">
                  ✅
                </text>
                <text class="status-symbol pending" wx:else>⭕</text>
              </view>
              <view class="ranking-link" bindtap="viewRanking" data-camp="{{item}}">
                <text class="ranking-text">排行榜</text>
              </view>
            </view>
          </view>
          <!-- 打卡进度 -->
          <view class="camp-progress">
            <view class="progress-data">
              <text class="data-item">第{{item.currentDay}}天</text>
              <text class="data-separator">•</text>
              <text class="data-item">连续{{item.streakDays}}天</text>
              <text class="data-separator">•</text>
              <text class="data-item">{{item.totalPoints}}积分</text>
              <text class="data-separator">•</text>
              <text class="data-item">完成{{item.progressPercent}}%</text>
            </view>
          </view>
          <!-- 家庭荣誉契约详情（如果有） -->
          <view class="contract-detail" wx:if="{{item.hasContract}}">
            <view class="contract-title">
              <text class="contract-icon">🏆</text>
              <text class="contract-text">家庭荣誉契约</text>
            </view>
            <view class="contract-info">
              <text class="contract-reward">🎁 {{item.contract.reward}}</text>
              <text class="contract-witness">👥 {{item.contract.witness}}</text>
            </view>
          </view>
          <!-- 操作按钮区 - 打卡与契约并排 -->
          <view class="action-buttons-row">
            <button class="btn btn-primary-soft checkin-btn-flex" bindtap="goToCampDetail" data-camp="{{item}}" wx:if="{{item.todayStatus === 'pending'}}">
              <text class="btn-icon">⚡</text>
              <text class="btn-text">立即打卡</text>
            </button>
            <button class="btn btn-success-soft checkin-btn-flex" bindtap="goToCampDetail" data-camp="{{item}}" wx:else>
              <text class="btn-icon">✅</text>
              <text class="btn-text">今日已打卡</text>
            </button>
            <button class="btn btn-outline-soft contract-btn-flex" bindtap="viewContract" data-camp="{{item}}">
              <text class="btn-icon">🤝</text>
              <text class="btn-text">{{item.hasContract ? '查看契约' : '添加契约'}}</text>
            </button>
          </view>
        </view>
      </view>
      <!-- 训练营空状态 -->
      <view class="camps-empty" wx:else>
        <view class="empty-icon">📚</view>
        <text class="empty-title">还没有参加训练营</text>
        <text class="empty-desc">去首页选择适合的训练营，开始学习之旅</text>
        <button class="empty-action-btn" bindtap="goToHome">
          <text class="btn-text">选择训练营</text>
        </button>
      </view>
    </view>
  </view>
  <!-- 成长Tab内容 -->
  <view class="tab-content" wx:if="{{currentTab === 1}}">
    <!-- 个人荣誉总览 -->
    <view class="honor-overview-section">
      <view class="section-title">
        <text class="title-icon">🌟</text>
        <text class="title-text">个人荣誉</text>
      </view>
      <view class="honor-stats">
        <view class="stat-item">
          <text class="stat-value">{{userStats.totalPoints}}</text>
          <text class="stat-label">总积分</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{userStats.currentLevel}}</text>
          <text class="stat-label">当前等级</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{userStats.streakDays}}</text>
          <text class="stat-label">连续天数</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{userStats.completedContracts}}</text>
          <text class="stat-label">完成契约</text>
        </view>
      </view>
    </view>
    <!-- 勋章墙 -->
    <view class="medals-section">
      <view class="section-header">
        <view class="section-title">
          <text class="title-icon">🏅</text>
          <text class="title-text">勋章墙</text>
          <text class="medals-count">{{unlockedMedals}}/{{totalMedals}}</text>
        </view>
        <button class="share-btn" bindtap="shareAllMedals" wx:if="{{unlockedMedals > 0}}">
          <text class="share-icon">🔗</text>
          <text class="share-text">分享</text>
        </button>
      </view>
      <!-- 勋章网格 -->
      <view class="medals-grid" wx:if="{{medalsList.length > 0}}">
        <view class="medal-item {{item.unlocked ? 'unlocked' : 'locked'}}" wx:for="{{medalsList}}" wx:key="id" bindtap="viewMedalDetail" data-medal="{{item}}">
          <view class="medal-content">
            <view class="medal-icon">{{item.icon}}</view>
            <text class="medal-name">{{item.name}}</text>
            <!-- 勋章等级标识 -->
            <view class="medal-level" wx:if="{{item.level_name}}">
              <text class="level-text">{{item.level_name}}</text>
            </view>
            <!-- 契约勋章特殊标识 -->
            <view class="medal-special" wx:if="{{item.is_contract_medal}}">
              <text class="special-label">契约</text>
            </view>
            <!-- 未解锁勋章的进度条 -->
            <view class="medal-progress" wx:if="{{!item.unlocked && item.target > 0}}">
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{(item.progress / item.target) * 100}}%"></view>
              </view>
              <text class="progress-text">{{item.progress}}/{{item.target}}</text>
            </view>
          </view>
        </view>
      </view>
      <!-- 勋章墙空状态 -->
      <view class="medals-empty" wx:elif="{{!isLoadingGrowthData}}">
        <text class="empty-title">还没有勋章</text>
        <text class="empty-desc">完成训练营打卡和契约挑战，获得专属勋章</text>
      </view>
      <!-- 勋章加载状态 -->
      <view class="loading-state" wx:else>
        <view class="loading-icon">⏳</view>
        <text class="loading-text">正在加载勋章数据...</text>
      </view>
    </view>
    <!-- 家庭荣誉契约 -->
    <view class="contracts-section">
      <view class="section-header">
        <view class="section-title">
          <text class="title-icon">🤝</text>
          <text class="title-text">家庭荣誉契约</text>
          <text class="contracts-count">{{activeContractsCount}}个进行中</text>
        </view>
        <!-- <button class="create-btn" bindtap="createNewContract">
          <text class="create-icon">+</text>
          <text class="create-text">创建</text>
        </button> -->
      </view>
      <!-- 契约列表 -->
      <view class="contracts-list" wx:if="{{contractsList.length > 0}}">
        <view class="contract-card" wx:for="{{contractsList}}" wx:key="id" bindtap="viewContractDetail" data-contract="{{item}}">
          <view class="contract-header">
            <view class="contract-title-row">
              <text class="contract-title">{{item.title}}</text>
              <view class="contract-status-badge {{item.contract_status === 1 ? 'active' : item.contract_status === 2 ? 'completed' : item.contract_status === 3 ? 'pending-award' : 'cancelled'}}">
                <text class="status-text">
                  {{item.contract_status === 1 ? '进行中' : item.contract_status === 2 ? '已完成' : item.contract_status === 3 ? '待授勋' : '已取消'}}
                </text>
              </view>
            </view>
            <text class="contract-goal">🎯 {{item.goal_description}}</text>
          </view>
          <view class="contract-body">
            <view class="contract-reward">
              <text class="reward-icon">🎁</text>
              <text class="reward-text">{{item.reward_description}}</text>
            </view>
            <view class="contract-progress">
              <view class="progress-info">
                <text class="progress-label">进度</text>
                <text class="progress-value">{{item.current_progress}}/{{item.goal_value}}</text>
              </view>
              <view class="progress-bar">
                <view class="progress-fill" style="width: {{(item.current_progress / item.goal_value) * 100}}%"></view>
              </view>
            </view>
            <!-- 见证人信息 -->
            <view class="contract-witnesses" wx:if="{{item.witnesses && item.witnesses.length > 0}}">
              <text class="witnesses-label">见证人：</text>
              <view class="witnesses-list">
                <view class="witness-item" wx:for="{{item.witnesses}}" wx:for-item="witness" wx:key="user_id">
                  <text class="witness-avatar">{{witness.avatar || '👤'}}</text>
                  <text class="witness-name">{{witness.nickname}}</text>
                </view>
              </view>
            </view>
          </view>
          <view class="contract-footer">
            <text class="contract-date">开始：{{item.start_date_formatted}}</text>
            <text class="contract-deadline">目标：{{item.target_date_formatted}}</text>
          </view>
        </view>
      </view>
      <!-- 契约空状态 -->
      <view class="contracts-empty" wx:elif="{{!isLoadingGrowthData}}">
        <text class="empty-title">还没有家庭荣誉契约</text>
        <text class="empty-desc">创建契约，和家人一起见证孩子的成长</text>
      </view>
      <!-- 契约加载状态 -->
      <view class="loading-state" wx:else>
        <view class="loading-icon">⏳</view>
        <text class="loading-text">正在加载契约数据...</text>
      </view>
    </view>
    <!-- 成长轨迹 -->
    <view class="growth-track-section">
      <view class="section-title">
        <text class="title-icon">📈</text>
        <text class="title-text">成长轨迹</text>
        <text class="track-count">{{growthTrackList.length}}条记录</text>
      </view>
      <!-- 成长轨迹时间线列表 -->
      <view class="growth-timeline" wx:if="{{growthTrackList.length > 0}}">
        <view class="timeline-item" wx:for="{{growthTrackList}}" wx:key="{{index}}">
          <!-- 时间线连接线 -->
          <view class="timeline-line" wx:if="{{index < growthTrackList.length - 1}}"></view>
          <!-- 时间线节点 -->
          <view class="timeline-node">
            <view class="node-icon {{item.type}}">
              <text class="icon-text">
                {{item.type === 'checkin' ? '✓' : item.type === 'contract' ? '🤝' : '🏆'}}
              </text>
            </view>
          </view>
          <!-- 成长记录卡片 -->
          <view class="timeline-content">
            <view class="track-card">
              <view class="track-header">
                <view class="track-main-info">
                  <text class="track-title">{{item.title}}</text>
                  <text class="track-desc">{{item.description}}</text>
                </view>
                <button class="share-poster-btn" bindtap="shareGrowthPoster" data-track="{{item}}" catchtap="true">
                  <text class="share-icon">📤</text>
                  <text class="share-text">分享</text>
                </button>
              </view>
              <view class="track-footer">
                <view class="track-meta">
                  <text class="track-date">{{item.date}}</text>
                  <text class="track-points" wx:if="{{item.points > 0}}">+{{item.points}}积分</text>
                </view>
                <view class="track-type-badge {{item.type}}">
                  <text class="badge-text">
                    {{item.type === 'checkin' ? '打卡记录' : item.type === 'contract' ? '契约完成' : '荣誉获得'}}
                  </text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
      <!-- 成长轨迹空状态 -->
      <view class="growth-track-empty" wx:elif="{{growthTrackList.length === 0 && !isLoadingGrowthData}}">
        <view class="empty-icon">📈</view>
        <text class="empty-title">暂无成长记录</text>
        <text class="empty-desc">完成训练营打卡和契约挑战，记录成长足迹</text>
        <button class="empty-action-btn" bindtap="goToHome">
          <text class="btn-text">开始成长</text>
        </button>
      </view>
      <!-- 成长轨迹加载状态 -->
      <view class="loading-state" wx:elif="{{isLoadingGrowthData}}">
        <view class="loading-icon">⏳</view>
        <text class="loading-text">正在加载成长数据...</text>
      </view>
    </view>
  </view>
  <!-- 任务Tab空状态 -->
  <view wx:if="{{currentTab === 0 && !hasActiveCamps}}" class="empty-state">
    <text class="empty-title">还没有参加训练营</text>
    <text class="empty-desc">去首页选择适合的训练营开始学习吧！</text>
  </view>
  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>