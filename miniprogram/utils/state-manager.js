// 状态管理器 - 基于微信小程序最佳实践
// 实现单一数据源、状态不可变性、响应式更新
// 遵循MVS规则：统一代码模板，模块化设计

const { userStorage, cacheStorage } = require("./storage.js");
const { createError, ERROR_CODES } = require("./error.js");

/**
 * 状态管理器类
 * 提供全局状态管理、响应式更新、状态持久化功能
 */
class StateManager {
  constructor() {
    // 初始状态
    this.state = {
      // 用户相关状态
      user: {
        isLoggedIn: false,
        token: "",
        userInfo: null,
      },

      // 孩子相关状态
      children: {
        currentChild: null,
        childrenList: [],
        loading: false,
      },

      // 应用相关状态
      app: {
        version: "1.1.0",
        theme: "light",
        language: "zh-CN",
      },

      // 业务数据状态
      business: {
        activeTasks: [],
        recentRecords: [],
        leaderboard: [],
        badges: [],
      },

      // UI状态
      ui: {
        loading: false,
        error: null,
        toast: null,
      },
    };

    // 订阅者列表
    this.subscribers = new Map();

    // 中间件列表
    this.middlewares = [];

    // 初始化
    this.init();
  }

  /**
   * 初始化状态管理器
   */
  init() {
    // 从本地存储恢复状态
    this.restoreState();

    // 添加默认中间件
    this.addMiddleware(this.loggingMiddleware);
    this.addMiddleware(this.persistenceMiddleware);
  }

  /**
   * 获取当前状态
   * @param {string} path 状态路径，如 'user.isLoggedIn'
   * @returns {any} 状态值
   */
  getState(path = "") {
    if (!path) return this.state;

    return path.split(".").reduce((obj, key) => {
      return obj && obj[key] !== undefined ? obj[key] : null;
    }, this.state);
  }

  /**
   * 更新状态
   * @param {string} path 状态路径
   * @param {any} value 新值
   * @param {Object} options 选项
   */
  setState(path, value, options = {}) {
    const { silent = false, persist = true } = options;

    try {
      // 创建新状态（保持不可变性）
      const newState = this.createNewState(path, value);

      // 执行中间件
      const action = { type: "SET_STATE", path, value, options };
      this.executeMiddlewares(action, this.state, newState);

      // 更新状态
      const oldState = this.state;
      this.state = newState;

      // 通知订阅者
      if (!silent) {
        this.notifySubscribers(path, value, oldState);
      }

      // 持久化状态
      if (persist) {
        this.persistState(path, value);
      }
    } catch (error) {
      console.error("setState error:", error);
      throw createError(ERROR_CODES.STATE_ERROR, "状态更新失败", error);
    }
  }

  /**
   * 创建新状态对象（保持不可变性）
   */
  createNewState(path, value) {
    const keys = path.split(".");
    const newState = JSON.parse(JSON.stringify(this.state));

    let current = newState;
    for (let i = 0; i < keys.length - 1; i++) {
      if (!current[keys[i]]) {
        current[keys[i]] = {};
      }
      current = current[keys[i]];
    }

    current[keys[keys.length - 1]] = value;
    return newState;
  }

  /**
   * 订阅状态变化
   * @param {string} path 状态路径
   * @param {Function} callback 回调函数
   * @returns {Function} 取消订阅函数
   */
  subscribe(path, callback) {
    if (!this.subscribers.has(path)) {
      this.subscribers.set(path, new Set());
    }

    this.subscribers.get(path).add(callback);

    // 返回取消订阅函数
    return () => {
      const pathSubscribers = this.subscribers.get(path);
      if (pathSubscribers) {
        pathSubscribers.delete(callback);
        if (pathSubscribers.size === 0) {
          this.subscribers.delete(path);
        }
      }
    };
  }

  /**
   * 取消订阅状态变化
   * @param {string} path 状态路径
   * @param {Function} callback 回调函数
   */
  unsubscribe(path, callback) {
    const pathSubscribers = this.subscribers.get(path);
    if (pathSubscribers) {
      pathSubscribers.delete(callback);
      if (pathSubscribers.size === 0) {
        this.subscribers.delete(path);
      }
    }
  }

  /**
   * 通知订阅者
   */
  notifySubscribers(path, value, oldState) {
    // 通知精确路径的订阅者
    const pathSubscribers = this.subscribers.get(path);
    if (pathSubscribers) {
      pathSubscribers.forEach((callback) => {
        try {
          const oldValue = path.split(".").reduce((obj, key) => {
            return obj && obj[key] !== undefined ? obj[key] : null;
          }, oldState);
          callback(value, oldValue);
        } catch (error) {
          console.error("Subscriber callback error:", error);
        }
      });
    }

    // 通知父路径的订阅者
    const pathParts = path.split(".");
    for (let i = pathParts.length - 1; i > 0; i--) {
      const parentPath = pathParts.slice(0, i).join(".");
      const parentSubscribers = this.subscribers.get(parentPath);
      if (parentSubscribers) {
        parentSubscribers.forEach((callback) => {
          try {
            const oldParentValue = parentPath.split(".").reduce((obj, key) => {
              return obj && obj[key] !== undefined ? obj[key] : null;
            }, oldState);
            callback(this.getState(parentPath), oldParentValue);
          } catch (error) {
            console.error("Parent subscriber callback error:", error);
          }
        });
      }
    }
  }

  /**
   * 添加中间件
   */
  addMiddleware(middleware) {
    this.middlewares.push(middleware);
  }

  /**
   * 执行中间件
   */
  executeMiddlewares(action, oldState, newState) {
    this.middlewares.forEach((middleware) => {
      try {
        middleware(action, oldState, newState);
      } catch (error) {
        console.error("Middleware error:", error);
      }
    });
  }

  /**
   * 日志中间件
   */
  loggingMiddleware = (action, oldState, newState) => {
    // 检查是否为开发环境（小程序环境中没有process对象）
    const isDevelopment =
      typeof process !== "undefined" &&
      process.env &&
      process.env.NODE_ENV === "development";

    if (isDevelopment || typeof wx !== "undefined") {
      console.group(`🔄 State Update: ${action.path}`);
      console.log("Action:", action);
      const oldValue = action.path.split(".").reduce((obj, key) => {
        return obj && obj[key] !== undefined ? obj[key] : null;
      }, oldState);
      console.log("Old Value:", oldValue);
      console.log("New Value:", action.value);
      console.groupEnd();
    }
  };

  /**
   * 持久化中间件
   */
  persistenceMiddleware = (action, oldState, newState) => {
    // 只持久化特定的状态
    const persistentPaths = [
      "user",
      "children.currentChild",
      "children.childrenList",
      "app",
    ];
    const shouldPersist = persistentPaths.some((path) =>
      action.path.startsWith(path)
    );

    if (shouldPersist && action.options.persist !== false) {
      this.persistState(action.path, action.value);
    }
  };

  /**
   * 持久化状态到本地存储
   */
  persistState(path, value) {
    try {
      // 根据路径选择合适的存储方式
      if (path.startsWith("user.userInfo")) {
        userStorage.setUserInfo(value);
      } else if (path.startsWith("user.token")) {
        userStorage.setToken(value);
      } else if (path.startsWith("children.currentChild")) {
        userStorage.setCurrentChild(value);
      } else if (path.startsWith("children.childrenList")) {
        userStorage.setChildrenList(value);
      } else {
        // 其他状态使用缓存存储
        cacheStorage.setCache(`state_${path}`, value);
      }
    } catch (error) {
      console.error("Persist state error:", error);
    }
  }

  /**
   * 从本地存储恢复状态
   */
  restoreState() {
    try {
      // 恢复用户状态
      const userInfo = userStorage.getUserInfo();
      const token = userStorage.getToken();
      if (userInfo && token) {
        this.state.user.isLoggedIn = true;
        this.state.user.userInfo = userInfo;
        this.state.user.token = token;
      }

      // 恢复孩子状态
      const currentChild = userStorage.getCurrentChild();
      const childrenList = userStorage.getChildrenList();
      if (currentChild) {
        this.state.children.currentChild = currentChild;
      }
      if (childrenList && childrenList.length > 0) {
        this.state.children.childrenList = childrenList;
      }

      console.log("✅ 状态恢复完成");
    } catch (error) {
      console.error("恢复状态失败:", error);
    }
  }

  /**
   * 清除所有状态
   */
  clearState() {
    this.state = {
      user: { isLoggedIn: false, token: "", userInfo: null },
      children: { currentChild: null, childrenList: [], loading: false },
      app: { version: "1.1.0", theme: "light", language: "zh-CN" },
      business: {
        activeTasks: [],
        recentRecords: [],
        leaderboard: [],
        badges: [],
      },
      ui: { loading: false, error: null, toast: null },
    };

    // 清除本地存储
    userStorage.clearUserData();

    // 通知所有订阅者
    this.subscribers.forEach((callbacks, path) => {
      callbacks.forEach((callback) => {
        try {
          callback(this.getState(path), null);
        } catch (error) {
          console.error("Clear state callback error:", error);
        }
      });
    });
  }
}

// 创建全局状态管理器实例
const stateManager = new StateManager();

// 导出状态管理器和便捷方法
module.exports = {
  stateManager,

  // 便捷的状态操作方法
  getState: (path) => stateManager.getState(path),
  setState: (path, value, options) =>
    stateManager.setState(path, value, options),
  subscribe: (path, callback) => stateManager.subscribe(path, callback),
  unsubscribe: (path, callback) => stateManager.unsubscribe(path, callback),
  clearState: () => stateManager.clearState(),
};
