// 状态操作方法 - 业务逻辑封装
// 提供高级的状态操作方法，封装常用的业务逻辑
// 遵循MVS规则：统一代码模板，基础测试

const { stateManager, getState, setState } = require("./state-manager.js");
const { createError, ERROR_CODES } = require("./error.js");
const { childDataStorage } = require("./storage.js");

/**
 * 用户相关状态操作
 */
const userActions = {
  /**
   * 用户登录
   * @param {Object} userInfo 用户信息
   * @param {string} token 登录令牌
   */
  login(userInfo, token) {
    try {
      setState("user.isLoggedIn", true);
      setState("user.userInfo", userInfo);
      setState("user.token", token);

      console.log("✅ 用户登录状态更新完成");
    } catch (error) {
      throw createError(ERROR_CODES.STATE_ERROR, "登录状态更新失败", error);
    }
  },

  /**
   * 用户登出
   */
  logout() {
    try {
      setState("user.isLoggedIn", false);
      setState("user.userInfo", null);
      setState("user.token", "");

      // 清除孩子相关状态
      setState("children.currentChild", null);
      setState("children.childrenList", []);

      console.log("✅ 用户登出状态更新完成");
    } catch (error) {
      throw createError(ERROR_CODES.STATE_ERROR, "登出状态更新失败", error);
    }
  },

  /**
   * 更新用户信息
   * @param {Object} userInfo 新的用户信息
   */
  updateUserInfo(userInfo) {
    try {
      const currentUserInfo = getState("user.userInfo") || {};
      const newUserInfo = { ...currentUserInfo, ...userInfo };
      setState("user.userInfo", newUserInfo);

      console.log("✅ 用户信息更新完成");
    } catch (error) {
      throw createError(ERROR_CODES.STATE_ERROR, "用户信息更新失败", error);
    }
  },

  /**
   * 检查登录状态
   * @returns {boolean} 是否已登录
   */
  isLoggedIn() {
    return getState("user.isLoggedIn") || false;
  },

  /**
   * 获取用户信息
   * @returns {Object|null} 用户信息
   */
  getUserInfo() {
    return getState("user.userInfo");
  },

  /**
   * 获取登录令牌
   * @returns {string} 登录令牌
   */
  getToken() {
    return getState("user.token") || "";
  },

  /**
   * 刷新用户信息
   * @returns {Promise<Object>} 用户信息
   */
  async refreshUserInfo() {
    try {
      // 动态导入API模块避免循环依赖
      const userAPI = require("../apis/user.js");
      const userInfo = await userAPI.getUserInfo();

      this.updateUserInfo(userInfo);
      console.log("✅ 用户信息刷新完成");

      return userInfo;
    } catch (error) {
      console.error("❌ 刷新用户信息失败:", error);
      throw createError(ERROR_CODES.STATE_ERROR, "刷新用户信息失败", error);
    }
  },
};

/**
 * 孩子相关状态操作
 */
const childrenActions = {
  /**
   * 设置当前选中的孩子
   * @param {Object} child 孩子信息
   */
  setCurrentChild(child) {
    try {
      setState("children.currentChild", child);
      console.log("✅ 当前孩子设置完成:", child?.name);

      // 触发孩子切换事件，用于清理其他孩子的缓存数据
      if (child && child.id) {
        this._onChildChanged(child.id);
      }
    } catch (error) {
      throw createError(ERROR_CODES.STATE_ERROR, "设置当前孩子失败", error);
    }
  },

  /**
   * 选择当前孩子并同步到服务器
   * @param {Object} child 孩子信息
   * @returns {Promise<void>}
   */
  async selectCurrentChildWithSync(child) {
    try {
      console.log("🔄 开始选择孩子并同步到服务器:", child?.name);

      // 1. 先更新本地状态（即使API失败，至少本地有反馈）
      this.setCurrentChild(child);

      // 2. 如果有孩子ID，调用API同步到服务器
      if (child && child.id) {
        // 动态导入API模块避免循环依赖
        const childrenAPI = require("../apis/children.js");
        await childrenAPI.selectCurrentChild(child.id);

        // 3. 更新用户信息中的current_child_id
        const currentUserInfo = userActions.getUserInfo() || {};
        const updatedUserInfo = {
          ...currentUserInfo,
          current_child_id: child.id,
        };
        userActions.updateUserInfo(updatedUserInfo);

        console.log("✅ 孩子选择同步完成:", child.name);
      } else {
        // 如果没有孩子ID，只更新用户信息中的current_child_id为0
        const currentUserInfo = userActions.getUserInfo() || {};
        const updatedUserInfo = {
          ...currentUserInfo,
          current_child_id: 0,
        };
        userActions.updateUserInfo(updatedUserInfo);

        console.log("✅ 孩子选择清空完成");
      }
    } catch (error) {
      console.error("❌ 孩子选择同步失败:", error);
      throw createError(ERROR_CODES.STATE_ERROR, "选择孩子同步失败", error);
    }
  },

  /**
   * 获取当前选中的孩子
   * @returns {Object|null} 当前孩子信息
   */
  getCurrentChild() {
    return getState("children.currentChild");
  },

  /**
   * 设置孩子列表
   * @param {Array} children 孩子列表
   */
  setChildrenList(children) {
    try {
      setState("children.childrenList", children || []);
      console.log("✅ 孩子列表更新完成，共", children?.length || 0, "个孩子");
    } catch (error) {
      throw createError(ERROR_CODES.STATE_ERROR, "设置孩子列表失败", error);
    }
  },

  /**
   * 获取孩子列表
   * @returns {Array} 孩子列表
   */
  getChildrenList() {
    return getState("children.childrenList") || [];
  },

  /**
   * 添加孩子
   * @param {Object} child 新孩子信息
   */
  addChild(child) {
    try {
      const currentChildren = this.getChildrenList();
      const newChildren = [...currentChildren, child];
      this.setChildrenList(newChildren);

      // 如果是第一个孩子，自动设为当前孩子
      if (newChildren.length === 1) {
        this.setCurrentChild(child);
      }

      console.log("✅ 孩子添加完成:", child.name);
    } catch (error) {
      throw createError(ERROR_CODES.STATE_ERROR, "添加孩子失败", error);
    }
  },

  /**
   * 更新孩子信息
   * @param {number} childId 孩子ID
   * @param {Object} updates 更新的信息
   */
  updateChild(childId, updates) {
    try {
      const children = this.getChildrenList();
      const updatedChildren = children.map((child) =>
        child.id === childId ? { ...child, ...updates } : child
      );
      this.setChildrenList(updatedChildren);

      // 如果更新的是当前孩子，也要更新当前孩子状态
      const currentChild = this.getCurrentChild();
      if (currentChild && currentChild.id === childId) {
        this.setCurrentChild({ ...currentChild, ...updates });
      }

      console.log("✅ 孩子信息更新完成:", childId);
    } catch (error) {
      throw createError(ERROR_CODES.STATE_ERROR, "更新孩子信息失败", error);
    }
  },

  /**
   * 删除孩子
   * @param {number} childId 孩子ID
   */
  removeChild(childId) {
    try {
      const children = this.getChildrenList();
      const filteredChildren = children.filter((child) => child.id !== childId);
      this.setChildrenList(filteredChildren);

      // 如果删除的是当前孩子，重新选择当前孩子
      const currentChild = this.getCurrentChild();
      if (currentChild && currentChild.id === childId) {
        const newCurrentChild =
          filteredChildren.length > 0 ? filteredChildren[0] : null;
        this.setCurrentChild(newCurrentChild);
      }

      console.log("✅ 孩子删除完成:", childId);
    } catch (error) {
      throw createError(ERROR_CODES.STATE_ERROR, "删除孩子失败", error);
    }
  },

  /**
   * 设置孩子加载状态
   * @param {boolean} loading 是否加载中
   */
  setLoading(loading) {
    setState("children.loading", loading);
  },

  /**
   * 孩子切换时的内部处理
   * @param {number} childId 新选中的孩子ID
   * @private
   */
  _onChildChanged(childId) {
    try {
      // 可以在这里添加切换孩子时的额外逻辑
      console.log("🔄 孩子切换事件:", childId);
    } catch (error) {
      console.error("孩子切换处理失败:", error);
    }
  },

  // === 儿童数据缓存管理方法 ===

  /**
   * 设置儿童任务数据到缓存
   * @param {number} childId 儿童ID
   * @param {Array} tasks 任务列表
   */
  setChildTasksCache(childId, tasks) {
    try {
      childDataStorage.setChildTasks(childId, tasks);
      console.log("✅ 儿童任务缓存设置完成:", childId);
    } catch (error) {
      throw createError(ERROR_CODES.STATE_ERROR, "设置儿童任务缓存失败", error);
    }
  },

  /**
   * 获取儿童任务数据从缓存
   * @param {number} childId 儿童ID
   * @returns {Array} 任务列表
   */
  getChildTasksCache(childId) {
    try {
      return childDataStorage.getChildTasks(childId);
    } catch (error) {
      console.error("获取儿童任务缓存失败:", error);
      return [];
    }
  },

  /**
   * 设置儿童记录数据到缓存
   * @param {number} childId 儿童ID
   * @param {Array} records 记录列表
   */
  setChildRecordsCache(childId, records) {
    try {
      childDataStorage.setChildRecords(childId, records);
      console.log("✅ 儿童记录缓存设置完成:", childId);
    } catch (error) {
      throw createError(ERROR_CODES.STATE_ERROR, "设置儿童记录缓存失败", error);
    }
  },

  /**
   * 获取儿童记录数据从缓存
   * @param {number} childId 儿童ID
   * @returns {Array} 记录列表
   */
  getChildRecordsCache(childId) {
    try {
      return childDataStorage.getChildRecords(childId);
    } catch (error) {
      console.error("获取儿童记录缓存失败:", error);
      return [];
    }
  },

  /**
   * 设置儿童徽章数据到缓存
   * @param {number} childId 儿童ID
   * @param {Array} badges 徽章列表
   */
  setChildBadgesCache(childId, badges) {
    try {
      childDataStorage.setChildBadges(childId, badges);
      console.log("✅ 儿童徽章缓存设置完成:", childId);
    } catch (error) {
      throw createError(ERROR_CODES.STATE_ERROR, "设置儿童徽章缓存失败", error);
    }
  },

  /**
   * 获取儿童徽章数据从缓存
   * @param {number} childId 儿童ID
   * @returns {Array} 徽章列表
   */
  getChildBadgesCache(childId) {
    try {
      return childDataStorage.getChildBadges(childId);
    } catch (error) {
      console.error("获取儿童徽章缓存失败:", error);
      return [];
    }
  },

  /**
   * 设置儿童积分数据到缓存
   * @param {number} childId 儿童ID
   * @param {Object} points 积分信息
   */
  setChildPointsCache(childId, points) {
    try {
      childDataStorage.setChildPoints(childId, points);
      console.log("✅ 儿童积分缓存设置完成:", childId);
    } catch (error) {
      throw createError(ERROR_CODES.STATE_ERROR, "设置儿童积分缓存失败", error);
    }
  },

  /**
   * 获取儿童积分数据从缓存
   * @param {number} childId 儿童ID
   * @returns {Object} 积分信息
   */
  getChildPointsCache(childId) {
    try {
      return childDataStorage.getChildPoints(childId);
    } catch (error) {
      console.error("获取儿童积分缓存失败:", error);
      return { total: 0, available: 0 };
    }
  },

  /**
   * 设置儿童统计数据到缓存
   * @param {number} childId 儿童ID
   * @param {Object} stats 统计信息
   */
  setChildStatsCache(childId, stats) {
    try {
      childDataStorage.setChildStats(childId, stats);
      console.log("✅ 儿童统计缓存设置完成:", childId);
    } catch (error) {
      throw createError(ERROR_CODES.STATE_ERROR, "设置儿童统计缓存失败", error);
    }
  },

  /**
   * 获取儿童统计数据从缓存
   * @param {number} childId 儿童ID
   * @returns {Object} 统计信息
   */
  getChildStatsCache(childId) {
    try {
      return childDataStorage.getChildStats(childId);
    } catch (error) {
      console.error("获取儿童统计缓存失败:", error);
      return {};
    }
  },

  /**
   * 清除指定儿童的所有缓存数据
   * @param {number} childId 儿童ID
   */
  clearChildCache(childId) {
    try {
      childDataStorage.clearChildData(childId);
      console.log("✅ 儿童缓存清除完成:", childId);
    } catch (error) {
      console.error("清除儿童缓存失败:", error);
    }
  },

  /**
   * 清除所有儿童的缓存数据
   */
  clearAllChildCache() {
    try {
      childDataStorage.clearAllChildData();
      console.log("✅ 所有儿童缓存清除完成");
    } catch (error) {
      console.error("清除所有儿童缓存失败:", error);
    }
  },

  /**
   * 获取孩子加载状态
   * @returns {boolean} 是否加载中
   */
  isLoading() {
    return getState("children.loading") || false;
  },

  /**
   * 刷新孩子列表数据
   * @returns {Promise<Array>} 孩子列表
   */
  async refreshChildrenList() {
    try {
      this.setLoading(true);

      // 动态导入API模块避免循环依赖
      const childrenAPI = require("../apis/children.js");
      const childrenList = await childrenAPI.getChildrenList();

      this.setChildrenList(childrenList);
      console.log("✅ 孩子列表刷新完成");

      return childrenList;
    } catch (error) {
      console.error("❌ 刷新孩子列表失败:", error);
      throw createError(ERROR_CODES.STATE_ERROR, "刷新孩子列表失败", error);
    } finally {
      this.setLoading(false);
    }
  },

  /**
   * 刷新当前孩子信息
   * @returns {Promise<Object|null>} 当前孩子信息
   */
  async refreshCurrentChild() {
    try {
      // 动态导入API模块避免循环依赖
      const childrenAPI = require("../apis/children.js");
      const currentChild = await childrenAPI.getCurrentChild();

      this.setCurrentChild(currentChild);
      console.log("✅ 当前孩子信息刷新完成");

      return currentChild;
    } catch (error) {
      console.error("❌ 刷新当前孩子信息失败:", error);
      throw createError(ERROR_CODES.STATE_ERROR, "刷新当前孩子信息失败", error);
    }
  },

  /**
   * 孩子创建成功后的数据同步
   * @param {Object} newChild 新创建的孩子信息
   * @returns {Promise<void>}
   */
  async syncAfterChildCreated(newChild) {
    try {
      console.log("🔄 开始同步孩子创建后的数据...", newChild);

      // 1. 添加到本地孩子列表（即使后续请求失败，至少本地有数据）
      if (newChild && newChild.id) {
        this.addChild(newChild);
      }

      // 2. 尝试刷新服务器数据（使用Promise.allSettled确保部分失败不影响其他操作）
      const results = await Promise.allSettled([
        this.refreshChildrenList(),
        this.refreshCurrentChild(),
        userActions.refreshUserInfo(),
      ]);

      // 记录各个操作的结果
      results.forEach((result, index) => {
        const operations = ["刷新孩子列表", "刷新当前孩子", "刷新用户信息"];
        if (result.status === "fulfilled") {
          console.log(`✅ ${operations[index]}成功`);
        } else {
          console.warn(`⚠️ ${operations[index]}失败:`, result.reason);
        }
      });

      console.log("✅ 孩子创建后数据同步完成");
    } catch (error) {
      console.error("❌ 孩子创建后数据同步失败:", error);
      // 不抛出错误，避免影响用户体验
      console.log("🔄 使用降级策略：仅更新本地缓存");
    }
  },

  /**
   * 孩子更新成功后的数据同步
   * @param {number} childId 孩子ID
   * @param {Object} updates 更新的信息
   * @returns {Promise<void>}
   */
  async syncAfterChildUpdated(childId, updates) {
    try {
      console.log("🔄 开始同步孩子更新后的数据...", { childId, updates });

      // 1. 更新本地孩子信息（即使后续请求失败，至少本地有数据）
      if (childId && updates) {
        this.updateChild(childId, updates);
      }

      // 2. 准备刷新操作
      const refreshOperations = [this.refreshChildrenList()];

      // 3. 如果更新的是当前孩子，也刷新当前孩子信息
      const currentChild = this.getCurrentChild();
      if (currentChild && currentChild.id === childId) {
        refreshOperations.push(this.refreshCurrentChild());
      }

      // 4. 执行刷新操作（使用Promise.allSettled确保部分失败不影响其他操作）
      const results = await Promise.allSettled(refreshOperations);

      // 记录操作结果
      const operations = ["刷新孩子列表"];
      if (refreshOperations.length > 1) {
        operations.push("刷新当前孩子");
      }

      results.forEach((result, index) => {
        if (result.status === "fulfilled") {
          console.log(`✅ ${operations[index]}成功`);
        } else {
          console.warn(`⚠️ ${operations[index]}失败:`, result.reason);
        }
      });

      console.log("✅ 孩子更新后数据同步完成");
    } catch (error) {
      console.error("❌ 孩子更新后数据同步失败:", error);
      // 不抛出错误，避免影响用户体验
      console.log("🔄 使用降级策略：仅更新本地缓存");
    }
  },
};

/**
 * 业务数据相关状态操作
 */
const businessActions = {
  /**
   * 设置活跃任务列表
   * @param {Array} tasks 任务列表
   */
  setActiveTasks(tasks) {
    setState("business.activeTasks", tasks || []);
  },

  /**
   * 获取活跃任务列表
   * @returns {Array} 任务列表
   */
  getActiveTasks() {
    return getState("business.activeTasks") || [];
  },

  /**
   * 设置最近记录
   * @param {Array} records 记录列表
   */
  setRecentRecords(records) {
    setState("business.recentRecords", records || []);
  },

  /**
   * 获取最近记录
   * @returns {Array} 记录列表
   */
  getRecentRecords() {
    return getState("business.recentRecords") || [];
  },

  /**
   * 设置排行榜数据
   * @param {Array} leaderboard 排行榜数据
   */
  setLeaderboard(leaderboard) {
    setState("business.leaderboard", leaderboard || []);
  },

  /**
   * 获取排行榜数据
   * @returns {Array} 排行榜数据
   */
  getLeaderboard() {
    return getState("business.leaderboard") || [];
  },

  /**
   * 设置徽章数据
   * @param {Array} badges 徽章列表
   */
  setBadges(badges) {
    setState("business.badges", badges || []);
  },

  /**
   * 获取徽章数据
   * @returns {Array} 徽章列表
   */
  getBadges() {
    return getState("business.badges") || [];
  },
};

/**
 * UI状态相关操作
 */
const uiActions = {
  /**
   * 设置全局加载状态
   * @param {boolean} loading 是否加载中
   */
  setLoading(loading) {
    setState("ui.loading", loading);
  },

  /**
   * 获取全局加载状态
   * @returns {boolean} 是否加载中
   */
  isLoading() {
    return getState("ui.loading") || false;
  },

  /**
   * 设置全局错误
   * @param {Error|string|null} error 错误信息
   */
  setError(error) {
    setState("ui.error", error);
  },

  /**
   * 获取全局错误
   * @returns {Error|string|null} 错误信息
   */
  getError() {
    return getState("ui.error");
  },

  /**
   * 清除全局错误
   */
  clearError() {
    setState("ui.error", null);
  },

  /**
   * 设置Toast消息
   * @param {Object} toast Toast配置
   */
  setToast(toast) {
    setState("ui.toast", toast);
  },

  /**
   * 获取Toast消息
   * @returns {Object|null} Toast配置
   */
  getToast() {
    return getState("ui.toast");
  },

  /**
   * 清除Toast消息
   */
  clearToast() {
    setState("ui.toast", null);
  },
};

/**
 * 应用相关状态操作
 */
const appActions = {
  /**
   * 设置主题
   * @param {string} theme 主题名称
   */
  setTheme(theme) {
    setState("app.theme", theme);
  },

  /**
   * 获取主题
   * @returns {string} 主题名称
   */
  getTheme() {
    return getState("app.theme") || "light";
  },

  /**
   * 设置语言
   * @param {string} language 语言代码
   */
  setLanguage(language) {
    setState("app.language", language);
  },

  /**
   * 获取语言
   * @returns {string} 语言代码
   */
  getLanguage() {
    return getState("app.language") || "zh-CN";
  },

  /**
   * 获取应用版本
   * @returns {string} 版本号
   */
  getVersion() {
    return getState("app.version") || "1.0.0";
  },
};

/**
 * 综合数据操作
 */
const dataActions = {
  /**
   * 刷新所有用户相关数据
   * @returns {Promise<void>}
   */
  async refreshAllUserData() {
    try {
      console.log("🔄 开始刷新所有用户相关数据...");

      // 并行刷新用户信息和孩子数据
      await Promise.all([
        userActions.refreshUserInfo(),
        childrenActions.refreshChildrenList(),
        childrenActions.refreshCurrentChild(),
      ]);

      console.log("✅ 所有用户相关数据刷新完成");
    } catch (error) {
      console.error("❌ 刷新用户数据失败:", error);
      throw createError(ERROR_CODES.STATE_ERROR, "刷新用户数据失败", error);
    }
  },

  /**
   * 初始化应用数据
   * @returns {Promise<void>}
   */
  async initializeAppData() {
    try {
      console.log("🚀 开始初始化应用数据...");

      // 检查登录状态
      if (!userActions.isLoggedIn()) {
        console.log("⚠️ 用户未登录，跳过数据初始化");
        return;
      }

      // 刷新所有数据
      await this.refreshAllUserData();

      console.log("✅ 应用数据初始化完成");
    } catch (error) {
      console.error("❌ 应用数据初始化失败:", error);
      throw createError(ERROR_CODES.STATE_ERROR, "应用数据初始化失败", error);
    }
  },
};

// 导出所有状态操作方法
module.exports = {
  userActions,
  childrenActions,
  businessActions,
  uiActions,
  appActions,
  dataActions,

  // 便捷访问
  stateManager,
  getState,
  setState,
};
