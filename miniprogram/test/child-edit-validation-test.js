// 孩子编辑功能参数验证测试
// 用于验证修复后的编辑功能是否正确处理所有必需字段

const dataConverter = require('../utils/data-converter.js');

// 模拟前端编辑表单数据
const mockEditFormData = {
  name: "小明",
  nickname: "明明",
  gender: "male",
  birthday: "2015-06-15",
  school: "实验小学",
  grade: "三年级2班",
  province: "北京",
  city: "海淀区",
  avatar: "👦",
  isEmojiAvatar: true
};

// 模拟当前孩子的完整数据（从后端获取）
const mockCurrentChildData = {
  id: 11,
  name: "小明",
  nickname: "明明",
  gender: "male",
  birthday: "2015-06-15",
  age: 8,
  school: "实验小学",
  grade: "三年级2班",
  province: "北京",
  city: "海淀区",
  avatar: "👦",
  isEmojiAvatar: true,
  skillLevel: 2,
  bestScore1Min: 50,
  bestScoreContinuous: 120,
  preferredDifficulty: 2,
  privacyLevel: 2,
  showInLeaderboard: 1,
  learningGoals: "提高跳绳技能",
  totalCheckins: 25,
  totalPoints: 1250,
  currentStreak: 7
};

console.log('=== 孩子编辑功能参数验证测试 ===');

// 测试1：基本编辑数据转换
console.log('\n1. 基本编辑数据转换测试');
const basicUpdateData = dataConverter.convertChildToBackendUpdate(mockEditFormData);
console.log('转换结果:', basicUpdateData);

// 验证必需字段
console.log('\n验证必需字段:');
console.log('- preferred_difficulty:', basicUpdateData.preferred_difficulty, '(应该是1-5之间的数字)');
console.log('- privacy_level:', basicUpdateData.privacy_level, '(应该是1-3之间的数字)');
console.log('- show_in_leaderboard:', basicUpdateData.show_in_leaderboard, '(应该是0或1)');

// 测试2：合并完整数据的转换
console.log('\n2. 合并完整数据转换测试');
const mergedData = {
  ...mockCurrentChildData,
  ...mockEditFormData
};
const fullUpdateData = dataConverter.convertChildToBackendUpdate(mergedData);
console.log('合并后转换结果:', fullUpdateData);

// 测试3：字段验证函数
console.log('\n3. 字段验证函数测试');

// 测试难度级别验证
const testDifficultyLevels = [0, 1, 2, 3, 4, 5, 6, '2', 'invalid', null, undefined];
console.log('难度级别验证测试:');
testDifficultyLevels.forEach(level => {
  const result = dataConverter.validateDifficultyLevel(level);
  console.log(`  输入: ${level} -> 输出: ${result}`);
});

// 测试隐私级别验证
const testPrivacyLevels = [0, 1, 2, 3, 4, '2', 'invalid', null, undefined];
console.log('\n隐私级别验证测试:');
testPrivacyLevels.forEach(level => {
  const result = dataConverter.validatePrivacyLevel(level);
  console.log(`  输入: ${level} -> 输出: ${result}`);
});

// 测试4：头像字段处理
console.log('\n4. 头像字段处理测试');

const avatarTestCases = [
  { avatar: '👦', isEmojiAvatar: true, expected: '👦' },
  { avatar: 'https://example.com/avatar.jpg', isEmojiAvatar: false, expected: 'https://example.com/avatar.jpg' },
  { avatar: 'invalid-url', isEmojiAvatar: false, expected: '' },
  { avatar: '', isEmojiAvatar: false, expected: '' },
  { avatar: undefined, isEmojiAvatar: false, expected: undefined }
];

avatarTestCases.forEach((testCase, index) => {
  const testData = {
    name: "测试",
    gender: "male",
    avatar: testCase.avatar,
    isEmojiAvatar: testCase.isEmojiAvatar
  };
  const result = dataConverter.convertChildToBackendUpdate(testData);
  console.log(`测试${index + 1}: 输入 ${JSON.stringify(testCase)} -> avatar: ${result.avatar}`);
});

// 测试5：边界情况
console.log('\n5. 边界情况测试');

// 空数据
const emptyResult = dataConverter.convertChildToBackendUpdate({});
console.log('空数据转换:', emptyResult);

// null/undefined数据
const nullResult = dataConverter.convertChildToBackendUpdate(null);
console.log('null数据转换:', nullResult);

// 只有部分字段
const partialData = { name: "小红", gender: "female" };
const partialResult = dataConverter.convertChildToBackendUpdate(partialData);
console.log('部分字段转换:', partialResult);

// 测试6：后端API要求验证
console.log('\n6. 后端API要求验证');
console.log('检查转换后的数据是否符合后端ChildrenUpdateRequest要求:');

const finalTestData = dataConverter.convertChildToBackendUpdate(mergedData);
const validationChecks = {
  'name字段': finalTestData.name && typeof finalTestData.name === 'string' && finalTestData.name.length <= 50,
  'gender字段': [0, 1, 2].includes(finalTestData.gender),
  'preferred_difficulty字段': [1, 2, 3, 4, 5].includes(finalTestData.preferred_difficulty),
  'privacy_level字段': [1, 2, 3].includes(finalTestData.privacy_level),
  'show_in_leaderboard字段': [0, 1].includes(finalTestData.show_in_leaderboard),
  'nickname字段': !finalTestData.nickname || finalTestData.nickname.length <= 50,
  'school字段': !finalTestData.school || finalTestData.school.length <= 100,
  'grade字段': !finalTestData.grade || finalTestData.grade.length <= 20,
  'province字段': !finalTestData.province || finalTestData.province.length <= 20,
  'city字段': !finalTestData.city || finalTestData.city.length <= 20
};

Object.entries(validationChecks).forEach(([field, isValid]) => {
  console.log(`  ${field}: ${isValid ? '✅ 通过' : '❌ 失败'}`);
});

console.log('\n=== 测试完成 ===');
console.log('最终转换数据:', JSON.stringify(finalTestData, null, 2));
