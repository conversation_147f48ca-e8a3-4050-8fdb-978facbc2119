// 测试API集成的简单脚本
// 用于验证前端页面能否正确调用新的API接口

const checkinAPI = require("./apis/checkin.js");

// 模拟测试数据
const testData = {
  campId: 1,
  childId: 1,
};

async function testCampCheckinCalendar() {
  console.log("=== 测试训练营打卡日历API ===");
  
  try {
    console.log("调用参数:", testData);
    
    const result = await checkinAPI.getCampCheckinCalendar(
      testData.campId, 
      testData.childId
    );
    
    console.log("API调用成功!");
    console.log("响应数据:", JSON.stringify(result, null, 2));
    
    // 验证响应数据结构
    if (result && result.data) {
      const { camp_info, calendar_data, makeup_info } = result.data;
      
      console.log("=== 数据结构验证 ===");
      console.log("训练营信息:", camp_info);
      console.log("日历数据:", calendar_data ? `${calendar_data.dates.length}天数据` : "无");
      console.log("补卡信息:", makeup_info);
      
      if (calendar_data && calendar_data.dates && calendar_data.dates.length > 0) {
        console.log("第一天数据示例:", calendar_data.dates[0]);
      }
      
      return true;
    } else {
      console.error("响应数据格式不正确");
      return false;
    }
    
  } catch (error) {
    console.error("API调用失败:", error);
    return false;
  }
}

// 如果在Node.js环境中运行
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    testCampCheckinCalendar
  };
}

// 如果在小程序环境中运行
if (typeof wx !== 'undefined') {
  // 可以在小程序中调用测试
  console.log("小程序环境检测到，可以调用 testCampCheckinCalendar() 进行测试");
}
