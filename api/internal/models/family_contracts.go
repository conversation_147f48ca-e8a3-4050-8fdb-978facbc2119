package models

import (
	"time"
)

// ==================== Model ====================

// FamilyContracts 家庭荣誉契约表：记录家庭契约的完整信息
type FamilyContracts struct {
	BaseModel
	ChildID           int64     `json:"child_id" gorm:"column:child_id;not null;default:0" validate:"required"`                            // 孩子ID，关联children.id
	CampID            int64     `json:"camp_id" gorm:"column:camp_id;not null;default:0" validate:"required"`                              // 训练营ID，关联training_camps.id
	CreatorUserID     int64     `json:"creator_user_id" gorm:"column:creator_user_id;not null;default:0" validate:"required"`              // 创建者用户ID，关联users.id
	Title             string    `json:"title" gorm:"column:title;size:200;not null" validate:"required,max=200"`                           // 契约标题
	GoalDescription   string    `json:"goal_description" gorm:"column:goal_description;size:500;not null" validate:"required,max=500"`     // 目标描述
	RewardDescription string    `json:"reward_description" gorm:"column:reward_description;size:500;not null" validate:"required,max=500"` // 奖励描述
	GoalType          int8      `json:"goal_type" gorm:"column:goal_type;not null;default:1" validate:"required"`                          // 目标类型 1:连续打卡 2:总打卡数 3:成绩达标
	GoalValue         int       `json:"goal_value" gorm:"column:goal_value;not null;default:0" validate:"required"`                        // 目标数值
	CurrentProgress   int       `json:"current_progress" gorm:"column:current_progress;not null;default:0" validate:"required"`            // 当前进度
	ContractStatus    int8      `json:"contract_status" gorm:"column:contract_status;not null;default:1" validate:"required"`              // 状态 1:进行中 2:已完成 3:待授勋 4:已授勋
	StartDate         time.Time `json:"start_date" gorm:"column:start_date;not null" validate:"required"`                                  // 开始日期
	TargetDate        time.Time `json:"target_date" gorm:"column:target_date;not null" validate:"required"`                                // 目标日期
	CompletedDate     time.Time `json:"completed_date" gorm:"column:completed_date"`                                                       // 完成日期，NULL表示未完成
	AwardedDate       time.Time `json:"awarded_date" gorm:"column:awarded_date"`                                                           // 授勋日期，NULL表示未授勋
}

// TableName 指定表名
func (FamilyContracts) TableName() string {
	return "family_contracts"
}

// ==================== Requests ====================

// FamilyContractsCreateRequest 创建家庭荣誉契约表：记录家庭契约的完整信息请求
type FamilyContractsCreateRequest struct {
	ChildID           int64     `json:"child_id" binding:"required" validate:"required"`                           // 孩子ID，关联children.id
	CampID            int64     `json:"camp_id" binding:"required" validate:"required"`                            // 训练营ID，关联training_camps.id
	CreatorUserID     int64     `json:"creator_user_id" binding:"required" validate:"required"`                    // 创建者用户ID，关联users.id
	Title             string    `json:"title" binding:"required,max=200" validate:"required,max=200"`              // 契约标题
	GoalDescription   string    `json:"goal_description" binding:"required,max=500" validate:"required,max=500"`   // 目标描述
	RewardDescription string    `json:"reward_description" binding:"required,max=500" validate:"required,max=500"` // 奖励描述
	GoalType          int8      `json:"goal_type" binding:"required" validate:"required"`                          // 目标类型 1:连续打卡 2:总打卡数 3:成绩达标
	GoalValue         int       `json:"goal_value" binding:"required" validate:"required"`                         // 目标数值
	CurrentProgress   int       `json:"current_progress" binding:"required" validate:"required"`                   // 当前进度
	ContractStatus    int8      `json:"contract_status" binding:"required" validate:"required"`                    // 状态 1:进行中 2:已完成 3:待授勋 4:已授勋
	StartDate         time.Time `json:"start_date" binding:"required" validate:"required"`                         // 开始日期
	TargetDate        time.Time `json:"target_date" binding:"required" validate:"required"`                        // 目标日期
	CompletedDate     time.Time `json:"completed_date"`                                                            // 完成日期，NULL表示未完成
	AwardedDate       time.Time `json:"awarded_date"`                                                              // 授勋日期，NULL表示未授勋
}

// FamilyContractsUpdateRequest 更新家庭荣誉契约表：记录家庭契约的完整信息请求
type FamilyContractsUpdateRequest struct {
	ChildID           *int64     `json:"child_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                           // 孩子ID，关联children.id
	CampID            *int64     `json:"camp_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                            // 训练营ID，关联training_camps.id
	CreatorUserID     *int64     `json:"creator_user_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                    // 创建者用户ID，关联users.id
	Title             *string    `json:"title,omitempty" binding:"omitempty,required,max=200" validate:"omitempty,required,max=200"`              // 契约标题
	GoalDescription   *string    `json:"goal_description,omitempty" binding:"omitempty,required,max=500" validate:"omitempty,required,max=500"`   // 目标描述
	RewardDescription *string    `json:"reward_description,omitempty" binding:"omitempty,required,max=500" validate:"omitempty,required,max=500"` // 奖励描述
	GoalType          *int8      `json:"goal_type,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                          // 目标类型 1:连续打卡 2:总打卡数 3:成绩达标
	GoalValue         *int       `json:"goal_value,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                         // 目标数值
	CurrentProgress   *int       `json:"current_progress,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                   // 当前进度
	ContractStatus    *int8      `json:"contract_status,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                    // 状态 1:进行中 2:已完成 3:待授勋 4:已授勋
	StartDate         *time.Time `json:"start_date,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                         // 开始日期
	TargetDate        *time.Time `json:"target_date,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                        // 目标日期
	CompletedDate     *time.Time `json:"completed_date,omitempty"`                                                                                // 完成日期，NULL表示未完成
	AwardedDate       *time.Time `json:"awarded_date,omitempty"`                                                                                  // 授勋日期，NULL表示未授勋
}

// ==================== Responses ====================

// FamilyContractsResponse 家庭荣誉契约表：记录家庭契约的完整信息响应
type FamilyContractsResponse struct {
	ID                uint      `json:"id"`                 // 主键ID
	ChildID           int64     `json:"child_id"`           // 孩子ID，关联children.id
	CampID            int64     `json:"camp_id"`            // 训练营ID，关联training_camps.id
	CreatorUserID     int64     `json:"creator_user_id"`    // 创建者用户ID，关联users.id
	Title             string    `json:"title"`              // 契约标题
	GoalDescription   string    `json:"goal_description"`   // 目标描述
	RewardDescription string    `json:"reward_description"` // 奖励描述
	GoalType          int8      `json:"goal_type"`          // 目标类型 1:连续打卡 2:总打卡数 3:成绩达标
	GoalValue         int       `json:"goal_value"`         // 目标数值
	CurrentProgress   int       `json:"current_progress"`   // 当前进度
	ContractStatus    int8      `json:"contract_status"`    // 状态 1:进行中 2:已完成 3:待授勋 4:已授勋
	StartDate         time.Time `json:"start_date"`         // 开始日期
	TargetDate        time.Time `json:"target_date"`        // 目标日期
	CompletedDate     time.Time `json:"completed_date"`     // 完成日期，NULL表示未完成
	AwardedDate       time.Time `json:"awarded_date"`       // 授勋日期，NULL表示未授勋
	CreatedAt         time.Time `json:"created_at"`         // 创建时间
	UpdatedAt         time.Time `json:"updated_at"`         // 更新时间
}

// FamilyContractsListResponse 家庭荣誉契约表：记录家庭契约的完整信息列表响应
type FamilyContractsListResponse struct {
	List  []*FamilyContractsResponse `json:"list"`  // 家庭荣誉契约表：记录家庭契约的完整信息列表
	Total int64                      `json:"total"` // 总数
}

// ==================== Converters ====================

// ToResponse 将家庭荣誉契约表：记录家庭契约的完整信息模型转换为响应结构
func (m *FamilyContracts) ToResponse() *FamilyContractsResponse {
	return &FamilyContractsResponse{
		ID:                m.ID,
		ChildID:           m.ChildID,
		CampID:            m.CampID,
		CreatorUserID:     m.CreatorUserID,
		Title:             m.Title,
		GoalDescription:   m.GoalDescription,
		RewardDescription: m.RewardDescription,
		GoalType:          m.GoalType,
		GoalValue:         m.GoalValue,
		CurrentProgress:   m.CurrentProgress,
		ContractStatus:    m.ContractStatus,
		StartDate:         m.StartDate,
		TargetDate:        m.TargetDate,
		CompletedDate:     m.CompletedDate,
		AwardedDate:       m.AwardedDate,
		CreatedAt:         m.CreatedAt,
		UpdatedAt:         m.UpdatedAt,
	}
}

// ToResponseList 将家庭荣誉契约表：记录家庭契约的完整信息模型列表转换为响应列表
func FamilyContractsToResponseList(models []*FamilyContracts, total int64) *FamilyContractsListResponse {
	list := make([]*FamilyContractsResponse, 0, len(models))
	for _, model := range models {
		list = append(list, model.ToResponse())
	}

	return &FamilyContractsListResponse{
		List:  list,
		Total: total,
	}
}

// ApplyUpdateRequest 应用更新请求到家庭荣誉契约表：记录家庭契约的完整信息模型
func (m *FamilyContracts) ApplyUpdateRequest(req *FamilyContractsUpdateRequest) {
	if req.ChildID != nil {
		m.ChildID = *req.ChildID
	}
	if req.CampID != nil {
		m.CampID = *req.CampID
	}
	if req.CreatorUserID != nil {
		m.CreatorUserID = *req.CreatorUserID
	}
	if req.Title != nil {
		m.Title = *req.Title
	}
	if req.GoalDescription != nil {
		m.GoalDescription = *req.GoalDescription
	}
	if req.RewardDescription != nil {
		m.RewardDescription = *req.RewardDescription
	}
	if req.GoalType != nil {
		m.GoalType = *req.GoalType
	}
	if req.GoalValue != nil {
		m.GoalValue = *req.GoalValue
	}
	if req.CurrentProgress != nil {
		m.CurrentProgress = *req.CurrentProgress
	}
	if req.ContractStatus != nil {
		m.ContractStatus = *req.ContractStatus
	}
	if req.StartDate != nil {
		m.StartDate = *req.StartDate
	}
	if req.TargetDate != nil {
		m.TargetDate = *req.TargetDate
	}
	if req.CompletedDate != nil {
		m.CompletedDate = *req.CompletedDate
	}
	if req.AwardedDate != nil {
		m.AwardedDate = *req.AwardedDate
	}
}
