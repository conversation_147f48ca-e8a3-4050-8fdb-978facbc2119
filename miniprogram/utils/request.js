// 网络请求封装 - 统一网络请求处理
// 遵循MVS规则：统一代码模板，完善错误处理，基础测试覆盖

const { API, ERROR_CODES } = require("./constants.js");
const { userStorage } = require("./storage.js");
const { handleNetworkError, createError } = require("./error.js");
const { authErrorDetector } = require("./auth-error-detector.js");
const { smartRetryManager } = require("./smart-retry.js");
const { uxOptimizer } = require("./ux-optimizer.js");
const { requestQueueManager } = require("./request-queue.js");
const { httpInjectionManager } = require("./http-injection.js");

/**
 * 请求拦截器管理
 */
class InterceptorManager {
  constructor() {
    this.handlers = [];
  }

  /**
   * 添加拦截器
   * @param {function} fulfilled 成功处理函数
   * @param {function} rejected 失败处理函数
   * @returns {number} 拦截器ID
   */
  use(fulfilled, rejected) {
    this.handlers.push({
      fulfilled,
      rejected,
    });
    return this.handlers.length - 1;
  }

  /**
   * 移除拦截器
   * @param {number} id 拦截器ID
   */
  eject(id) {
    if (this.handlers[id]) {
      this.handlers[id] = null;
    }
  }

  /**
   * 执行拦截器
   * @param {any} value 要处理的值
   * @returns {Promise} 处理结果
   */
  async execute(value) {
    let result = value;

    for (const handler of this.handlers) {
      if (handler) {
        try {
          if (handler.fulfilled) {
            result = await handler.fulfilled(result);
          }
        } catch (error) {
          if (handler.rejected) {
            result = await handler.rejected(error);
          } else {
            throw error;
          }
        }
      }
    }

    return result;
  }
}

/**
 * HTTP请求客户端
 */
class HttpClient {
  constructor() {
    this.baseURL = API.BASE_URL;
    this.timeout = API.TIMEOUT;
    this.retryCount = API.RETRY_COUNT;
    this.retryDelay = API.RETRY_DELAY;

    // 拦截器
    this.interceptors = {
      request: new InterceptorManager(),
      response: new InterceptorManager(),
    };

    // 令牌刷新状态
    this.isRefreshing = false;
    this.failedQueue = [];
    this.refreshRetryCount = 0;
    this.maxRefreshRetries = 1;

    // 新增：错误检测结果缓存和增强功能
    this._lastAuthErrorDetection = null;

    // 依赖注入管理器引用
    this.injectionManager = httpInjectionManager;

    // 设置默认拦截器
    this._setupDefaultInterceptors();
  }

  /**
   * 设置默认拦截器
   */
  _setupDefaultInterceptors() {
    // 请求拦截器 - 添加认证头
    this.interceptors.request.use(
      (config) => {
        const token = userStorage.getToken();
        const currentChild = userStorage.getCurrentChild();
        if (token) {
          config.header = {
            ...config.header,
            Authorization: `Bearer ${token}`,
            "X-Children-ID": currentChild?.id || 0,
          };
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // 响应拦截器 - 统一处理响应和token刷新
    this.interceptors.response.use(
      (response) => {
        // Debug: 打印响应数据
        console.log("📥 API响应:", {
          url: response.config?.url || "unknown",
          statusCode: response.statusCode,
          data: response.data,
        });

        // 检查业务状态码
        if (response.data && response.data.code !== undefined) {
          if (response.data.code === 0) {
            const result = response.data.data || response.data;
            console.log("✅ API成功返回:", result);
            return result;
          } else {
            console.error("❌ API业务错误:", response.data);

            // 🔧 修复：检查是否为JWT认证相关错误
            if (this._isAuthError(null, response)) {
              // 创建带有正确状态码的认证错误
              const authError = createError(
                ERROR_CODES.UNAUTHORIZED,
                response.data.message || "认证失败",
                null,
                {
                  response,
                  statusCode: 401, // 确保状态码为401
                  config: response.config,
                }
              );
              throw authError;
            }

            // 其他业务错误正常处理
            throw createError(
              ERROR_CODES.SERVER_ERROR,
              response.data.message || "服务器返回错误",
              null,
              { response }
            );
          }
        }
        console.log("✅ API直接返回:", response.data);
        return response.data;
      },
      async (error) => {
        console.error("❌ API请求失败:", error);

        // 🔧 修复：增强的认证错误检测
        if (
          this._isAuthError(error, error.response) &&
          !error.config?.url?.includes("/auth/refresh")
        ) {
          return this._handleTokenExpired(error);
        }

        return Promise.reject(handleNetworkError(error));
      }
    );
  }

  /**
   * 发送请求
   * @param {object} config 请求配置
   * @returns {Promise} 请求结果
   */
  async request(config) {
    // 合并默认配置
    const finalConfig = {
      url: this.baseURL + config.url,
      method: config.method || "GET",
      data: config.data || {},
      header: {
        "Content-Type": "application/json",
        ...config.header,
      },
      timeout: config.timeout || this.timeout,
      ...config,
    };

    // 🔧 新增：用户体验优化
    const operationId = `${finalConfig.method}_${
      finalConfig.url
    }_${Date.now()}`;
    const scenario = config.scenario || this._detectScenario(finalConfig);
    const uxController = uxOptimizer.startOperation(
      operationId,
      scenario,
      config.uxOptions
    );

    try {
      // Debug: 打印请求参数
      console.log("🚀 API请求:", {
        operationId,
        scenario,
        url: finalConfig.url,
        method: finalConfig.method,
        data: finalConfig.data,
        header: finalConfig.header,
      });

      // 执行请求拦截器
      const processedConfig = await this.interceptors.request.execute(
        finalConfig
      );

      // 🔧 新增：智能重试机制
      const result = await smartRetryManager.executeWithRetry(
        (config) => this._executeRequest(config, uxController),
        processedConfig
      );

      uxController.complete(result);
      return result;
    } catch (error) {
      uxController.fail(error);
      throw error;
    }
  }

  /**
   * 检测请求场景
   * @param {Object} config 请求配置
   * @returns {string} 场景类型
   */
  _detectScenario(config) {
    const url = config.url.toLowerCase();
    const method = config.method.toUpperCase();

    // 关键操作：支付、重要数据提交
    if (
      url.includes("/payment") ||
      url.includes("/order") ||
      (url.includes("/submit") && method === "POST")
    ) {
      return "critical";
    }

    // 数据提交：表单、设置等
    if (method === "POST" || method === "PUT" || method === "PATCH") {
      return "submission";
    }

    // 浏览操作：GET请求
    if (method === "GET") {
      return "browsing";
    }

    return "browsing";
  }

  /**
   * 执行请求（集成队列管理）
   * @param {Object} config 请求配置
   * @param {Object} uxController UX控制器
   * @returns {Promise} 请求结果
   */
  async _executeRequest(config, uxController) {
    // 如果正在刷新token，使用队列管理
    if (this.isRefreshing) {
      return requestQueueManager.enqueue(
        (config) => this._makeDirectRequest(config, uxController),
        config,
        { priority: config.priority || "normal" }
      );
    }

    return this._makeDirectRequest(config, uxController);
  }

  /**
   * 直接执行请求
   * @param {Object} config 请求配置
   * @param {Object} uxController UX控制器
   * @returns {Promise} 请求结果
   */
  async _makeDirectRequest(config, uxController) {
    try {
      const response = await this._makeRequest(config);
      return await this.interceptors.response.execute(response);
    } catch (error) {
      // 如果是认证错误，特殊处理
      if (this._isAuthError(error, error.response)) {
        return this._handleTokenExpired(error);
      }
      throw error;
    }
  }

  /**
   * 带重试机制的请求
   * @param {object} config 请求配置
   * @param {number} retryCount 剩余重试次数
   * @returns {Promise} 请求结果
   */
  async _requestWithRetry(config, retryCount = this.retryCount) {
    try {
      const response = await this._makeRequest(config);
      return await this.interceptors.response.execute(response);
    } catch (error) {
      // 如果是网络错误且还有重试次数，则重试
      if (retryCount > 0 && this._shouldRetry(error)) {
        console.log(
          `Request failed, retrying... (${retryCount} attempts left)`
        );
        await this._delay(this.retryDelay);
        return this._requestWithRetry(config, retryCount - 1);
      }
      throw error;
    }
  }

  /**
   * 发送实际请求
   * @param {object} config 请求配置
   * @returns {Promise} 请求结果
   */
  _makeRequest(config) {
    return new Promise((resolve, reject) => {
      wx.request({
        ...config,
        success: (res) => {
          if (res.statusCode >= 200 && res.statusCode < 300) {
            resolve(res);
          } else {
            reject({
              statusCode: res.statusCode,
              data: res.data,
              message: `HTTP ${res.statusCode}`,
            });
          }
        },
        fail: (error) => {
          // 开发环境下提供更友好的错误信息
          let errorMessage = error.errMsg || "Network Error";
          if (
            errorMessage.includes("Failed to fetch") ||
            errorMessage.includes("ERR_CONNECTION_REFUSED")
          ) {
            errorMessage =
              "无法连接到开发服务器，请确保API服务器已启动 (localhost:8080)";
          }

          reject({
            message: errorMessage,
            error,
          });
        },
      });
    });
  }

  /**
   * 判断是否应该重试
   * @param {object} error 错误对象
   * @returns {boolean} 是否应该重试
   */
  _shouldRetry(error) {
    // 网络错误或超时错误可以重试
    return (
      error.message?.includes("timeout") ||
      error.message?.includes("network") ||
      error.statusCode >= 500
    );
  }

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   * @returns {Promise} 延迟Promise
   */
  _delay(ms) {
    return new Promise((resolve) => setTimeout(resolve, ms));
  }

  // HTTP方法快捷方式
  get(url, config = {}) {
    // 处理GET请求的params参数，转换为query string
    if (config.params) {
      const queryString = Object.keys(config.params)
        .filter(
          (key) =>
            config.params[key] !== undefined && config.params[key] !== null
        )
        .map(
          (key) =>
            `${encodeURIComponent(key)}=${encodeURIComponent(
              config.params[key]
            )}`
        )
        .join("&");

      if (queryString) {
        url = url + (url.includes("?") ? "&" : "?") + queryString;
      }

      // 移除params，避免传递到data中
      const { params, ...restConfig } = config;
      config = restConfig;
    }

    return this.request({ ...config, method: "GET", url });
  }

  post(url, data, config = {}) {
    return this.request({ ...config, method: "POST", url, data });
  }

  put(url, data, config = {}) {
    return this.request({ ...config, method: "PUT", url, data });
  }

  delete(url, config = {}) {
    return this.request({ ...config, method: "DELETE", url });
  }

  patch(url, data, config = {}) {
    return this.request({ ...config, method: "PATCH", url, data });
  }

  /**
   * 检查是否为认证相关错误
   * @param {Object} error 错误对象
   * @param {Object} response 响应对象
   * @returns {boolean} 是否为认证错误
   */
  _isAuthError(error, response) {
    const detection = authErrorDetector.detectAuthError(error, response);

    if (detection.isAuthError) {
      console.log("🔍 认证错误检测结果:", {
        errorType: detection.errorType,
        confidence: detection.confidence,
        message: detection.message,
        details: detection.details,
      });

      // 存储检测结果供后续使用
      this._lastAuthErrorDetection = detection;
      return true;
    }

    return false;
  }

  /**
   * 处理token过期
   * @param {Object} error 原始错误
   * @returns {Promise} 处理结果
   */
  async _handleTokenExpired(error) {
    const originalRequest = error.config;

    console.log("🔄 处理token过期:", {
      url: originalRequest?.url,
      method: originalRequest?.method,
      errorType: error.statusCode ? "HTTP错误" : "业务错误",
      errorCode: error.response?.data?.code,
      errorMessage: error.message,
    });

    // 🔧 新增：使用队列管理器处理并发请求
    if (this.isRefreshing) {
      console.log("🔄 Token正在刷新中，使用队列管理器");
      return requestQueueManager.enqueue(
        (config) => this._makeDirectRequest(config),
        originalRequest,
        { priority: "high" } // 认证失败的请求优先级较高
      );
    }

    this.isRefreshing = true;

    try {
      const refreshToken = userStorage.getRefreshToken();

      if (!refreshToken) {
        console.log("🔄 没有refresh token，跳转登录页");
        this._redirectToLogin("refresh_token_missing");
        return Promise.reject(handleNetworkError(error));
      }

      console.log("🔄 开始刷新token");

      // 🔧 使用依赖注入的token刷新处理函数
      const tokenRefreshHandler =
        this.injectionManager.getTokenRefreshHandler();
      if (!tokenRefreshHandler) {
        console.error("❌ Token刷新处理函数未注入");
        this._redirectToLogin("token_refresh_handler_missing");
        return Promise.reject(handleNetworkError(error));
      }

      // 🔧 新增：通知队列管理器开始token刷新
      const refreshPromise = tokenRefreshHandler(refreshToken);
      requestQueueManager.startTokenRefresh(refreshPromise);

      // 等待刷新完成
      const tokenData = await refreshPromise;

      console.log("✅ Token刷新成功:", {
        hasAccessToken: !!tokenData.access_token,
        hasRefreshToken: !!tokenData.refresh_token,
        expiresIn: tokenData.expires_in,
      });

      // 更新存储的token
      userStorage.setToken(tokenData.access_token);
      userStorage.setRefreshToken(tokenData.refresh_token);

      // 更新状态管理中的token
      const { userActions } = require("./state-actions.js");
      const userInfo = userActions.getUserInfo();
      if (userInfo) {
        userActions.login(
          userInfo,
          tokenData.access_token,
          tokenData.refresh_token
        );
      }

      // 处理队列中的请求
      this._processQueue(null, tokenData.access_token);

      // 重试原始请求
      originalRequest.header = {
        ...originalRequest.header,
        Authorization: `Bearer ${tokenData.access_token}`,
      };

      console.log("🔄 重试原始请求:", originalRequest.url);
      return this.request(originalRequest);
    } catch (refreshError) {
      console.error("❌ Token刷新失败:", {
        error: refreshError.message,
        statusCode: refreshError.statusCode,
        errorCode: refreshError.response?.data?.code,
      });

      // 处理队列中的请求（全部失败）
      this._processQueue(refreshError, null);

      // 根据刷新失败的原因决定处理方式
      const failureReason = this._analyzeRefreshFailure(refreshError);
      this._redirectToLogin(failureReason);

      return Promise.reject(handleNetworkError(error));
    } finally {
      this.isRefreshing = false;
      console.log("🔄 Token刷新流程结束");
    }
  }

  /**
   * 处理请求队列
   * @param {Error} error 错误对象
   * @param {string} token 新的token
   */
  _processQueue(error, token) {
    this.failedQueue.forEach(({ resolve, reject, config }) => {
      if (error) {
        reject(error);
      } else {
        config.header = {
          ...config.header,
          Authorization: `Bearer ${token}`,
        };
        resolve(this.request(config));
      }
    });

    this.failedQueue = [];
  }

  /**
   * 分析刷新失败的原因
   * @param {Object} refreshError 刷新错误对象
   * @returns {string} 失败原因
   */
  _analyzeRefreshFailure(refreshError) {
    if (refreshError.statusCode === 401) {
      return "refresh_token_expired";
    } else if (refreshError.statusCode === 403) {
      return "refresh_token_invalid";
    } else if (refreshError.message?.includes("network")) {
      return "network_error";
    } else {
      return "unknown_error";
    }
  }

  /**
   * 跳转到登录页
   * @param {string} reason 跳转原因
   */
  _redirectToLogin(reason = "token_expired") {
    console.log("🔄 跳转登录页，原因:", reason);

    // 清除用户数据
    userStorage.clearUserData();

    // 🔧 使用依赖注入的登录重定向处理函数
    const loginRedirectHandler =
      this.injectionManager.getLoginRedirectHandler();
    if (loginRedirectHandler) {
      console.log("✅ 使用注入的登录重定向处理函数");
      loginRedirectHandler(reason);
      return;
    }

    // 🔧 降级处理：如果没有注入处理函数，使用默认逻辑
    console.log("⚠️ 使用默认登录重定向逻辑");
    this._defaultRedirectToLogin(reason);
  }

  /**
   * 默认的登录重定向逻辑
   * @param {string} reason 跳转原因
   */
  _defaultRedirectToLogin(reason = "token_expired") {
    // 根据不同原因显示不同提示
    const messages = {
      token_expired: "登录已过期，请重新登录",
      refresh_token_missing: "登录信息丢失，请重新登录",
      refresh_token_expired: "登录已过期，请重新登录",
      refresh_token_invalid: "登录信息无效，请重新登录",
      network_error: "网络异常，请检查网络后重新登录",
      unknown_error: "登录异常，请重新登录",
      token_refresh_handler_missing: "系统配置异常，请重新登录",
    };

    const message = messages[reason] || messages.token_expired;

    // 显示提示
    wx.showToast({
      title: message,
      icon: "none",
      duration: 2000,
    });

    // 延迟跳转，让用户看到提示
    setTimeout(() => {
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const currentRoute = currentPage ? currentPage.route : "";

      // 构建重定向URL，包含原因参数
      const redirectUrl = encodeURIComponent(`/${currentRoute}`);

      wx.reLaunch({
        url: `/pages/login/login?redirectUrl=${redirectUrl}&reason=${reason}`,
      });
    }, 1000);
  }
}

// 创建默认实例
const http = new HttpClient();

/**
 * 上传文件
 * @param {string} url 上传地址
 * @param {string} filePath 文件路径
 * @param {object} options 上传选项
 * @returns {Promise} 上传结果
 */
function uploadFile(url, filePath, options = {}) {
  return new Promise((resolve, reject) => {
    const token = userStorage.getToken();

    wx.uploadFile({
      url: http.baseURL + url,
      filePath,
      name: options.name || "file",
      formData: options.formData || {},
      header: {
        Authorization: token ? `Bearer ${token}` : "",
        ...options.header,
      },
      success: (res) => {
        try {
          const data = JSON.parse(res.data);
          if (data.code === 0) {
            resolve(data.data);
          } else {
            reject(createError(ERROR_CODES.SERVER_ERROR, data.message));
          }
        } catch (error) {
          reject(createError(ERROR_CODES.SERVER_ERROR, "上传响应解析失败"));
        }
      },
      fail: (error) => {
        // 检查是否为401错误，如果是则尝试刷新token后重试
        if (error.statusCode === 401) {
          console.log("🔄 上传文件遇到401错误，尝试刷新token");
          // 这里可以考虑实现上传文件的token刷新逻辑
          // 为了简化，暂时直接跳转登录
          http._redirectToLogin();
        }
        reject(handleNetworkError(error, { url, filePath }));
      },
    });
  });
}

/**
 * 下载文件
 * @param {string} url 下载地址
 * @param {object} options 下载选项
 * @returns {Promise} 下载结果
 */
function downloadFile(url, options = {}) {
  return new Promise((resolve, reject) => {
    wx.downloadFile({
      url: url.startsWith("http") ? url : http.baseURL + url,
      header: options.header || {},
      success: (res) => {
        if (res.statusCode === 200) {
          resolve(res.tempFilePath);
        } else {
          reject(createError(ERROR_CODES.NETWORK_ERROR, "文件下载失败"));
        }
      },
      fail: (error) => {
        reject(handleNetworkError(error, { url }));
      },
    });
  });
}

/**
 * 请求加载状态管理
 */
class LoadingManager {
  constructor() {
    this.loadingCount = 0;
    this.loadingTitle = "加载中...";
  }

  /**
   * 显示加载状态
   * @param {string} title 加载提示文字
   */
  show(title = this.loadingTitle) {
    if (this.loadingCount === 0) {
      wx.showLoading({
        title,
        mask: true,
      });
    }
    this.loadingCount++;
  }

  /**
   * 隐藏加载状态
   */
  hide() {
    this.loadingCount = Math.max(0, this.loadingCount - 1);
    if (this.loadingCount === 0) {
      wx.hideLoading();
    }
  }

  /**
   * 强制隐藏加载状态
   */
  forceHide() {
    this.loadingCount = 0;
    wx.hideLoading();
  }
}

// 创建全局加载管理器
const loading = new LoadingManager();

// 为HTTP客户端添加加载状态管理
http.interceptors.request.use((config) => {
  if (config.showLoading !== false) {
    loading.show(config.loadingTitle);
  }
  return config;
});

http.interceptors.response.use(
  (response) => {
    loading.hide();
    return response;
  },
  (error) => {
    loading.hide();
    return Promise.reject(error);
  }
);

// 导出HTTP客户端实例和工具函数
module.exports = {
  default: http,
  http,
  HttpClient,
  uploadFile,
  downloadFile,
  LoadingManager,
  loading,
};
