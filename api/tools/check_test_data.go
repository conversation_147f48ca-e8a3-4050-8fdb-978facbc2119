package main

import (
	"fmt"
	"log"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func main() {
	// 数据库连接配置
	dsn := "admin:235lwx123456@tcp(47.120.16.235:3306)/hop_planet_dev?charset=utf8mb4&parseTime=True&loc=Local"
	
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	fmt.Println("=== 检查测试数据 ===")

	// 检查训练营数据
	var campCount int64
	err = db.Raw("SELECT COUNT(*) FROM training_camps WHERE id = 1").Scan(&campCount).Error
	if err != nil {
		log.Printf("Error checking training_camps: %v", err)
	} else {
		fmt.Printf("Training camps with id=1: %d\n", campCount)
	}

	// 检查用户参与记录
	var participationCount int64
	err = db.Raw("SELECT COUNT(*) FROM user_camp_participations WHERE camp_id = 1 AND child_id = 1").Scan(&participationCount).Error
	if err != nil {
		log.Printf("Error checking user_camp_participations: %v", err)
	} else {
		fmt.Printf("User camp participations (camp_id=1, child_id=1): %d\n", participationCount)
	}

	// 检查打卡日期数据
	var checkinDatesCount int64
	err = db.Raw("SELECT COUNT(*) FROM camp_checkin_dates WHERE participation_id = 1").Scan(&checkinDatesCount).Error
	if err != nil {
		log.Printf("Error checking camp_checkin_dates: %v", err)
	} else {
		fmt.Printf("Camp checkin dates for participation_id=1: %d\n", checkinDatesCount)
	}

	// 如果缺少训练营数据，创建一个
	if campCount == 0 {
		fmt.Println("Creating basic training camp data...")
		createCampSQL := `
		INSERT INTO training_camps (
			id, title, subtitle, description, cover_image, 
			duration_days, difficulty_level, target_age_min, target_age_max,
			price, original_price, status, category_id,
			created_at, updated_at
		) VALUES (
			1, '21天跳绳挑战', '从零基础到连续跳绳300个', 
			'专为儿童设计的跳绳训练营，循序渐进提升跳绳技能', 
			'https://example.com/cover.jpg',
			21, 1, 6, 12,
			99.00, 199.00, 1, 1,
			NOW(), NOW()
		) ON DUPLICATE KEY UPDATE id=id;`
		
		err = db.Exec(createCampSQL).Error
		if err != nil {
			log.Printf("Failed to create training camp: %v", err)
		} else {
			fmt.Println("Training camp created successfully!")
		}
	}

	// 如果缺少打卡日期数据，创建一些
	if checkinDatesCount == 0 && participationCount > 0 {
		fmt.Println("Creating basic checkin dates data...")
		createCheckinDatesSQL := `
		INSERT INTO camp_checkin_dates (
			participation_id, day_number, checkin_date, status, date_type,
			created_at, updated_at
		) VALUES 
		(1, 1, '2024-01-15', 2, 1, NOW(), NOW()),
		(1, 2, '2024-01-16', 2, 1, NOW(), NOW()),
		(1, 3, '2024-01-17', 2, 1, NOW(), NOW()),
		(1, 4, '2024-01-18', 2, 1, NOW(), NOW()),
		(1, 5, '2024-01-19', 2, 1, NOW(), NOW()),
		(1, 6, '2024-01-20', 2, 2, NOW(), NOW()),
		(1, 7, '2024-01-21', 2, 2, NOW(), NOW()),
		(1, 8, '2024-01-22', 2, 1, NOW(), NOW()),
		(1, 9, '2024-01-23', 2, 1, NOW(), NOW()),
		(1, 10, '2024-01-24', 2, 1, NOW(), NOW()),
		(1, 11, '2024-01-25', 2, 1, NOW(), NOW()),
		(1, 12, '2024-01-26', 2, 1, NOW(), NOW()),
		(1, 13, '2024-01-27', 2, 2, NOW(), NOW()),
		(1, 14, '2024-01-28', 1, 2, NOW(), NOW()),
		(1, 15, '2024-01-29', 1, 1, NOW(), NOW()),
		(1, 16, '2024-01-30', 1, 1, NOW(), NOW()),
		(1, 17, '2024-01-31', 1, 1, NOW(), NOW()),
		(1, 18, '2024-02-01', 1, 1, NOW(), NOW()),
		(1, 19, '2024-02-02', 1, 1, NOW(), NOW()),
		(1, 20, '2024-02-03', 1, 2, NOW(), NOW()),
		(1, 21, '2024-02-04', 1, 2, NOW(), NOW());`
		
		err = db.Exec(createCheckinDatesSQL).Error
		if err != nil {
			log.Printf("Failed to create checkin dates: %v", err)
		} else {
			fmt.Println("Checkin dates created successfully!")
		}
	}

	fmt.Println("=== 数据检查完成 ===")
}
