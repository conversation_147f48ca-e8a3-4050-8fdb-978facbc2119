package models

import (
	"time"
)

// ==================== Model ====================

// GrowthTracks 成长轨迹记录表：记录孩子的成长里程碑
type GrowthTracks struct {
	BaseModel
	ChildID              int64  `json:"child_id" gorm:"column:child_id;not null;default:0" validate:"required"`                          // 孩子ID，关联children.id
	ParticipationID      int64  `json:"participation_id" gorm:"column:participation_id;not null;default:0" validate:"required"`          // 参与记录ID，关联user_camp_participations.id
	MilestoneType        int8   `json:"milestone_type" gorm:"column:milestone_type;not null;default:1" validate:"required"`              // 里程碑类型 1:首次打卡 2:连续打卡 3:完成训练营 4:获得勋章
	MilestoneTitle       string `json:"milestone_title" gorm:"column:milestone_title;size:200;not null" validate:"required,max=200"`     // 里程碑标题
	MilestoneDescription string `json:"milestone_description" gorm:"column:milestone_description;size:65535" validate:"max=65535"`       // 里程碑描述
	MilestoneIcon        string `json:"milestone_icon" gorm:"column:milestone_icon;size:50;not null" validate:"required,max=50"`         // 里程碑图标
	RelatedID            int64  `json:"related_id" gorm:"column:related_id;not null;default:0" validate:"required"`                      // 关联ID
	RelatedType          string `json:"related_type" gorm:"column:related_type;size:50;not null" validate:"required,max=50"`             // 关联类型
	AchievementValue     string `json:"achievement_value" gorm:"column:achievement_value;size:100;not null" validate:"required,max=100"` // 成就数值
	AchievementDetails   string `json:"achievement_details" gorm:"column:achievement_details"`                                           // 成就详细数据
	IsShareable          int8   `json:"is_shareable" gorm:"column:is_shareable;not null;default:1" validate:"required"`                  // 是否可分享 0:否 1:是
}

// TableName 指定表名
func (GrowthTracks) TableName() string {
	return "growth_tracks"
}

// ==================== Requests ====================

// GrowthTracksCreateRequest 创建成长轨迹记录表：记录孩子的成长里程碑请求
type GrowthTracksCreateRequest struct {
	ChildID              int64  `json:"child_id" binding:"required" validate:"required"`                          // 孩子ID，关联children.id
	ParticipationID      int64  `json:"participation_id" binding:"required" validate:"required"`                  // 参与记录ID，关联user_camp_participations.id
	MilestoneType        int8   `json:"milestone_type" binding:"required" validate:"required"`                    // 里程碑类型 1:首次打卡 2:连续打卡 3:完成训练营 4:获得勋章
	MilestoneTitle       string `json:"milestone_title" binding:"required,max=200" validate:"required,max=200"`   // 里程碑标题
	MilestoneDescription string `json:"milestone_description" binding:"max=65535" validate:"max=65535"`           // 里程碑描述
	MilestoneIcon        string `json:"milestone_icon" binding:"required,max=50" validate:"required,max=50"`      // 里程碑图标
	RelatedID            int64  `json:"related_id" binding:"required" validate:"required"`                        // 关联ID
	RelatedType          string `json:"related_type" binding:"required,max=50" validate:"required,max=50"`        // 关联类型
	AchievementValue     string `json:"achievement_value" binding:"required,max=100" validate:"required,max=100"` // 成就数值
	AchievementDetails   string `json:"achievement_details"`                                                      // 成就详细数据
	IsShareable          int8   `json:"is_shareable" binding:"required" validate:"required"`                      // 是否可分享 0:否 1:是
}

// GrowthTracksUpdateRequest 更新成长轨迹记录表：记录孩子的成长里程碑请求
type GrowthTracksUpdateRequest struct {
	ChildID              *int64  `json:"child_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                          // 孩子ID，关联children.id
	ParticipationID      *int64  `json:"participation_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                  // 参与记录ID，关联user_camp_participations.id
	MilestoneType        *int8   `json:"milestone_type,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                    // 里程碑类型 1:首次打卡 2:连续打卡 3:完成训练营 4:获得勋章
	MilestoneTitle       *string `json:"milestone_title,omitempty" binding:"omitempty,required,max=200" validate:"omitempty,required,max=200"`   // 里程碑标题
	MilestoneDescription *string `json:"milestone_description,omitempty" binding:"omitempty,max=65535" validate:"omitempty,max=65535"`           // 里程碑描述
	MilestoneIcon        *string `json:"milestone_icon,omitempty" binding:"omitempty,required,max=50" validate:"omitempty,required,max=50"`      // 里程碑图标
	RelatedID            *int64  `json:"related_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                        // 关联ID
	RelatedType          *string `json:"related_type,omitempty" binding:"omitempty,required,max=50" validate:"omitempty,required,max=50"`        // 关联类型
	AchievementValue     *string `json:"achievement_value,omitempty" binding:"omitempty,required,max=100" validate:"omitempty,required,max=100"` // 成就数值
	AchievementDetails   *string `json:"achievement_details,omitempty"`                                                                          // 成就详细数据
	IsShareable          *int8   `json:"is_shareable,omitempty" binding:"omitempty,required" validate:"omitempty,required"`                      // 是否可分享 0:否 1:是
}

// ==================== Responses ====================

// GrowthTracksResponse 成长轨迹记录表：记录孩子的成长里程碑响应
type GrowthTracksResponse struct {
	ID                   uint      `json:"id"`                    // 主键ID
	ChildID              int64     `json:"child_id"`              // 孩子ID，关联children.id
	ParticipationID      int64     `json:"participation_id"`      // 参与记录ID，关联user_camp_participations.id
	MilestoneType        int8      `json:"milestone_type"`        // 里程碑类型 1:首次打卡 2:连续打卡 3:完成训练营 4:获得勋章
	MilestoneTitle       string    `json:"milestone_title"`       // 里程碑标题
	MilestoneDescription string    `json:"milestone_description"` // 里程碑描述
	MilestoneIcon        string    `json:"milestone_icon"`        // 里程碑图标
	RelatedID            int64     `json:"related_id"`            // 关联ID
	RelatedType          string    `json:"related_type"`          // 关联类型
	AchievementValue     string    `json:"achievement_value"`     // 成就数值
	AchievementDetails   string    `json:"achievement_details"`   // 成就详细数据
	IsShareable          int8      `json:"is_shareable"`          // 是否可分享 0:否 1:是
	CreatedAt            time.Time `json:"created_at"`            // 创建时间
	UpdatedAt            time.Time `json:"updated_at"`            // 更新时间
}

// GrowthTracksListResponse 成长轨迹记录表：记录孩子的成长里程碑列表响应
type GrowthTracksListResponse struct {
	List  []*GrowthTracksResponse `json:"list"`  // 成长轨迹记录表：记录孩子的成长里程碑列表
	Total int64                   `json:"total"` // 总数
}

// ==================== Converters ====================

// ToResponse 将成长轨迹记录表：记录孩子的成长里程碑模型转换为响应结构
func (m *GrowthTracks) ToResponse() *GrowthTracksResponse {
	return &GrowthTracksResponse{
		ID:                   m.ID,
		ChildID:              m.ChildID,
		ParticipationID:      m.ParticipationID,
		MilestoneType:        m.MilestoneType,
		MilestoneTitle:       m.MilestoneTitle,
		MilestoneDescription: m.MilestoneDescription,
		MilestoneIcon:        m.MilestoneIcon,
		RelatedID:            m.RelatedID,
		RelatedType:          m.RelatedType,
		AchievementValue:     m.AchievementValue,
		AchievementDetails:   m.AchievementDetails,
		IsShareable:          m.IsShareable,
		CreatedAt:            m.CreatedAt,
	}
}

// ToResponseList 将成长轨迹记录表：记录孩子的成长里程碑模型列表转换为响应列表
func GrowthTracksToResponseList(models []*GrowthTracks, total int64) *GrowthTracksListResponse {
	list := make([]*GrowthTracksResponse, 0, len(models))
	for _, model := range models {
		list = append(list, model.ToResponse())
	}

	return &GrowthTracksListResponse{
		List:  list,
		Total: total,
	}
}

// ApplyUpdateRequest 应用更新请求到成长轨迹记录表：记录孩子的成长里程碑模型
func (m *GrowthTracks) ApplyUpdateRequest(req *GrowthTracksUpdateRequest) {
	if req.ChildID != nil {
		m.ChildID = *req.ChildID
	}
	if req.ParticipationID != nil {
		m.ParticipationID = *req.ParticipationID
	}
	if req.MilestoneType != nil {
		m.MilestoneType = *req.MilestoneType
	}
	if req.MilestoneTitle != nil {
		m.MilestoneTitle = *req.MilestoneTitle
	}
	if req.MilestoneDescription != nil {
		m.MilestoneDescription = *req.MilestoneDescription
	}
	if req.MilestoneIcon != nil {
		m.MilestoneIcon = *req.MilestoneIcon
	}
	if req.RelatedID != nil {
		m.RelatedID = *req.RelatedID
	}
	if req.RelatedType != nil {
		m.RelatedType = *req.RelatedType
	}
	if req.AchievementValue != nil {
		m.AchievementValue = *req.AchievementValue
	}
	if req.AchievementDetails != nil {
		m.AchievementDetails = *req.AchievementDetails
	}
	if req.IsShareable != nil {
		m.IsShareable = *req.IsShareable
	}
}
