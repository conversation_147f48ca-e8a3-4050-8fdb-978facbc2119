package api

import (
	"encoding/json"
	"kids-platform/internal/models"
	"kids-platform/internal/models/dto"
	"kids-platform/internal/repositories"
	"kids-platform/pkg/errcode"
	"kids-platform/pkg/logger"
	"time"
)

// CheckinService 打卡服务接口
type CheckinService interface {
	// 创建打卡记录
	CreateCheckin(req *models.CheckinCreateRequest) (*models.CheckinResponse, error)
	// 获取打卡历史
	GetCheckinHistory(childID uint, campID uint, offset, limit int) (*models.CheckinHistoryResponse, error)
	// 获取今日打卡状态
	GetTodayCheckinStatus(childID uint, campID uint) (*models.TodayCheckinStatus, error)
	// 获取打卡统计（指定训练营）
	GetCheckinStats(childID uint, campID uint) (*models.CheckinStatsResponse, error)
	// 获取打卡统计（所有训练营）
	GetCheckinStatsAll(childID uint) (*models.CheckinStatsResponse, error)
	// 获取训练营打卡日历
	GetCampCheckinCalendar(childID uint, campID uint) (*dto.CampCheckinCalendarResponse, error)
	// 初始化训练营打卡日期
	InitializeCampCheckinDates(participationID int64, campID uint, startDate time.Time, totalDays int) error
}

// checkinService 打卡服务实现
type checkinService struct {
	checkinRecordsRepo        repositories.CheckinRecordsRepository
	userCampParticipationRepo repositories.UserCampParticipationsRepository
	trainingCampsRepo         repositories.TrainingCampsRepository
	campCheckinDatesRepo      repositories.CampCheckinDatesRepository
}

// NewCheckinService 创建打卡服务
func NewCheckinService(
	checkinRecordsRepo repositories.CheckinRecordsRepository,
	userCampParticipationRepo repositories.UserCampParticipationsRepository,
	trainingCampsRepo repositories.TrainingCampsRepository,
	campCheckinDatesRepo repositories.CampCheckinDatesRepository,
) CheckinService {
	return &checkinService{
		checkinRecordsRepo:        checkinRecordsRepo,
		userCampParticipationRepo: userCampParticipationRepo,
		trainingCampsRepo:         trainingCampsRepo,
		campCheckinDatesRepo:      campCheckinDatesRepo,
	}
}

// ==================== 服务方法实现 ====================

// ==================== 服务方法实现 ====================

// CreateCheckin 创建打卡记录
func (s *checkinService) CreateCheckin(req *models.CheckinCreateRequest) (*models.CheckinResponse, error) {
	// 1. 解析打卡日期（支持指定日期打卡）
	var checkinDate time.Time
	var err error

	if req.CheckinDate != "" {
		// 使用指定的打卡日期
		checkinDate, err = time.Parse("2006-01-02", req.CheckinDate)
		if err != nil {
			logger.Error("Invalid checkin date format", "checkin_date", req.CheckinDate, "error", err)
			return nil, errcode.ErrValidation.WithDetails("打卡日期格式错误，请使用YYYY-MM-DD格式")
		}
	} else {
		// 默认使用今天
		today := time.Now().Format("2006-01-02")
		checkinDate, _ = time.Parse("2006-01-02", today)
	}

	// 2. 获取用户训练营参与记录
	participation, err := s.userCampParticipationRepo.GetByCampAndChild(req.CampID, req.ChildID)
	if err != nil {
		if err == errcode.ErrDataNotFound {
			logger.Error("User camp participation not found", "child_id", req.ChildID, "camp_id", req.CampID)
			return nil, errcode.ErrValidation.WithDetails("用户未参与该训练营")
		}
		logger.Error("Failed to get user camp participation", "child_id", req.ChildID, "camp_id", req.CampID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取训练营参与记录失败")
	}

	// 3. 检查指定日期是否已打卡（基于participation_id）
	existing, err := s.checkinRecordsRepo.GetByChildAndDateAndParticipation(req.ChildID, checkinDate, int64(participation.ID))
	if err != nil && err != errcode.ErrDataNotFound {
		logger.Error("Failed to check existing checkin", "child_id", req.ChildID, "date", checkinDate.Format("2006-01-02"), "participation_id", participation.ID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("检查打卡状态失败")
	}

	// 如果该日期已经打卡，返回已存在的打卡记录（业务状态，不是错误）
	if existing != nil {
		logger.Info("Checkin already exists for date", "child_id", req.ChildID, "date", checkinDate.Format("2006-01-02"), "participation_id", participation.ID, "checkin_id", existing.ID)

		return &models.CheckinResponse{
			CheckinRecordsResponse: existing.ToResponse(),
			PointsEarned:           existing.PointsEarned,
			Message:                "今天已经打卡了",
			Status:                 "already_checked_in", // 业务状态标识
		}, nil
	}

	// 4. 计算获得积分（基础积分 + 时长奖励）
	pointsEarned := s.calculatePoints(req.PracticeDuration, req.JumpCount1min)

	// 5. 创建打卡记录
	checkinRecord := &models.CheckinRecords{
		ChildID:             int64(req.ChildID),
		CampID:              int64(req.CampID),
		UserID:              int64(req.UserID),
		ParticipationID:     int64(participation.ID), // 设置参与记录ID
		CheckinDate:         checkinDate,             // 使用解析后的打卡日期
		PracticeDuration:    req.PracticeDuration,
		JumpCount1min:       req.JumpCount1min,
		JumpCountContinuous: req.JumpCountContinuous,
		FeelingText:         req.FeelingText,
		FeelingScore:        req.FeelingScore,
		PointsEarned:        pointsEarned,
		Status:              req.Status, // 使用请求中的状态（1:正常 2:补卡）
	}

	// 6. 处理照片数组（转换为JSON字符串存储到数据库）
	photosJSON := ""
	if len(req.Photos) > 0 {
		// 将[]string转换为JSON字符串存储
		photosBytes, err := json.Marshal(req.Photos)
		if err != nil {
			logger.Error("Failed to marshal photos to JSON", "photos", req.Photos, "error", err)
			return nil, errcode.ErrValidation.WithDetails("照片数据格式错误")
		}
		photosJSON = string(photosBytes)
	} else {
		// 空数组情况，存储空的JSON数组字符串
		photosJSON = "[]"
	}
	checkinRecord.Photos = photosJSON

	// 7. 使用事务处理多表更新，确保数据一致性
	// TODO: 这里应该使用数据库事务，但当前简化处理，先创建打卡记录
	if err := s.checkinRecordsRepo.Create(checkinRecord); err != nil {
		logger.Error("Failed to create checkin record", "child_id", req.ChildID, "participation_id", participation.ID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("创建打卡记录失败")
	}

	// 8. 更新训练营参与进度
	if err := s.updateCampProgressWithParticipation(participation, pointsEarned, req.PracticeDuration); err != nil {
		logger.Error("Failed to update camp progress", "child_id", req.ChildID, "camp_id", req.CampID, "participation_id", participation.ID, "error", err)
		// 进度更新失败不影响打卡成功，只记录错误
	}

	// 9. 记录积分变动（TODO: 需要实现积分记录逻辑）
	// TODO: 创建积分记录到 point_records 表
	// TODO: 更新成长轨迹到 growth_tracks 表

	logger.Info("Checkin created successfully",
		"child_id", req.ChildID,
		"camp_id", req.CampID,
		"participation_id", participation.ID,
		"checkin_date", checkinDate.Format("2006-01-02"),
		"points", pointsEarned,
		"status", req.Status)

	return &models.CheckinResponse{
		CheckinRecordsResponse: checkinRecord.ToResponse(),
		PointsEarned:           pointsEarned,
		Message:                "打卡成功！",
		Status:                 "success", // 业务状态标识
	}, nil
}

// GetCheckinHistory 获取打卡历史
func (s *checkinService) GetCheckinHistory(childID uint, campID uint, offset, limit int) (*models.CheckinHistoryResponse, error) {
	records, total, err := s.checkinRecordsRepo.GetByChildIDAndCampID(childID, campID, offset, limit)
	if err != nil {
		logger.Error("Failed to get checkin history", "child_id", childID, "camp_id", campID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取打卡历史失败")
	}

	var responseList []*models.CheckinRecordsResponse
	for _, record := range records {
		responseList = append(responseList, record.ToResponse())
	}

	logger.Info("Checkin history retrieved successfully", "child_id", childID, "camp_id", campID, "total", total)
	return &models.CheckinHistoryResponse{
		List:  responseList,
		Total: total,
	}, nil
}

// GetTodayCheckinStatus 获取今日打卡状态
func (s *checkinService) GetTodayCheckinStatus(childID uint, campID uint) (*models.TodayCheckinStatus, error) {
	today := time.Now().Format("2006-01-02")
	todayDate, _ := time.Parse("2006-01-02", today)

	// 使用新的方法，按训练营过滤查询今日打卡记录
	checkin, err := s.checkinRecordsRepo.GetByChildAndDateByCampID(childID, campID, todayDate)
	if err != nil {
		// 检查是否为"数据不存在"错误
		if errCodeErr, ok := err.(*errcode.Error); ok && errCodeErr.Code() == errcode.ErrDataNotFound.Code() {
			// 数据不存在是正常情况，表示今天还未打卡
		} else {
			// 其他错误为系统错误
			logger.Error("Failed to get today checkin status", "child_id", childID, "camp_id", campID, "error", err)
			return nil, errcode.ErrDatabase.WithDetails("获取今日打卡状态失败")
		}
	}

	status := &models.TodayCheckinStatus{
		HasCheckedIn: checkin != nil,
	}

	if checkin != nil {
		status.CheckinData = checkin.ToResponse()
	}

	return status, nil
}

// GetCheckinStats 获取打卡统计
func (s *checkinService) GetCheckinStats(childID uint, campID uint) (*models.CheckinStatsResponse, error) {
	// 获取指定训练营的所有打卡记录用于统计
	records, _, err := s.checkinRecordsRepo.GetByChildIDAndCampID(childID, campID, 0, 1000) // 获取足够多的记录用于统计
	if err != nil {
		logger.Error("Failed to get checkin records for stats", "child_id", childID, "camp_id", campID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取打卡统计失败")
	}

	stats := &models.CheckinStatsResponse{
		TotalCheckins:      len(records),
		ConsecutiveDays:    s.calculateConsecutiveDays(records),
		MaxConsecutiveDays: s.calculateMaxConsecutiveDays(records),
		TotalStudyMinutes:  s.calculateTotalStudyMinutes(records),
		AverageFeeling:     s.calculateAverageFeeling(records),
		ThisWeekCheckins:   s.calculateThisWeekCheckins(records),
		ThisMonthCheckins:  s.calculateThisMonthCheckins(records),
	}

	logger.Info("Checkin stats calculated successfully", "child_id", childID, "camp_id", campID, "total_checkins", stats.TotalCheckins)
	return stats, nil
}

// GetCheckinStatsAll 获取打卡统计（所有训练营）
func (s *checkinService) GetCheckinStatsAll(childID uint) (*models.CheckinStatsResponse, error) {
	// 获取所有打卡记录用于统计
	records, _, err := s.checkinRecordsRepo.GetByChildID(childID, 0, 1000) // 获取足够多的记录用于统计
	if err != nil {
		logger.Error("Failed to get checkin records for stats", "child_id", childID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取打卡统计失败")
	}

	stats := &models.CheckinStatsResponse{
		TotalCheckins:      len(records),
		ConsecutiveDays:    s.calculateConsecutiveDays(records),
		MaxConsecutiveDays: s.calculateMaxConsecutiveDays(records),
		TotalStudyMinutes:  s.calculateTotalStudyMinutes(records),
		AverageFeeling:     s.calculateAverageFeeling(records),
		ThisWeekCheckins:   s.calculateThisWeekCheckins(records),
		ThisMonthCheckins:  s.calculateThisMonthCheckins(records),
	}

	logger.Info("Checkin stats (all camps) calculated successfully", "child_id", childID, "total_checkins", stats.TotalCheckins)
	return stats, nil
}

// ==================== 辅助方法 ====================

// calculatePoints 计算打卡获得的积分
func (s *checkinService) calculatePoints(duration int, jumpCount int) int {
	basePoints := 10                  // 基础积分
	durationBonus := duration / 5 * 2 // 每5分钟额外2积分
	jumpBonus := jumpCount / 50 * 5   // 每50个跳绳额外5积分

	total := basePoints + durationBonus + jumpBonus
	if total > 100 {
		total = 100 // 单次打卡最多100积分
	}

	return total
}

// updateCampProgress 更新训练营参与进度
func (s *checkinService) updateCampProgress(childID uint, campID uint, pointsEarned int) error {
	participation, err := s.userCampParticipationRepo.GetByCampAndChild(campID, childID)
	if err != nil {
		return err
	}

	// 更新统计数据
	participation.TotalCheckins++
	participation.TotalStudyMinutes += pointsEarned // 这里需要传入实际的学习时长

	// 计算进度百分比
	camp, err := s.trainingCampsRepo.GetByID(campID)
	if err == nil {
		participation.ProgressPercentage = float64(participation.TotalCheckins) / float64(camp.DurationDays) * 100
		if participation.ProgressPercentage > 100 {
			participation.ProgressPercentage = 100
		}
	}

	// 更新当前天数
	if participation.TotalCheckins > participation.CurrentDay {
		participation.CurrentDay = participation.TotalCheckins
	}

	return s.userCampParticipationRepo.Update(participation.ID, participation)
}

// updateCampProgressWithParticipation 使用已有的参与记录更新训练营进度
func (s *checkinService) updateCampProgressWithParticipation(participation *models.UserCampParticipations, pointsEarned int, studyMinutes int) error {
	// 更新统计数据
	participation.TotalCheckins++
	participation.TotalStudyMinutes += studyMinutes // 使用实际的学习时长

	// 计算进度百分比
	camp, err := s.trainingCampsRepo.GetByID(uint(participation.CampID))
	if err == nil {
		participation.ProgressPercentage = float64(participation.TotalCheckins) / float64(camp.DurationDays) * 100
		if participation.ProgressPercentage > 100 {
			participation.ProgressPercentage = 100
		}
	}

	// 更新当前天数
	if participation.TotalCheckins > participation.CurrentDay {
		participation.CurrentDay = participation.TotalCheckins
	}

	return s.userCampParticipationRepo.Update(participation.ID, participation)
}

// calculateConsecutiveDays 计算连续打卡天数
func (s *checkinService) calculateConsecutiveDays(records []*models.CheckinRecords) int {
	if len(records) == 0 {
		return 0
	}

	// 按日期排序（最新的在前）
	// 这里简化处理，假设records已经按日期排序
	consecutive := 1

	for i := 0; i < len(records)-1; i++ {
		current := records[i].CheckinDate
		next := records[i+1].CheckinDate

		// 检查是否连续
		if current.Sub(next).Hours() <= 24 {
			consecutive++
		} else {
			break
		}
	}

	return consecutive
}

// calculateMaxConsecutiveDays 计算最大连续天数
func (s *checkinService) calculateMaxConsecutiveDays(records []*models.CheckinRecords) int {
	// 简化实现，返回当前连续天数
	return s.calculateConsecutiveDays(records)
}

// calculateTotalStudyMinutes 计算总学习时长
func (s *checkinService) calculateTotalStudyMinutes(records []*models.CheckinRecords) int {
	total := 0
	for _, record := range records {
		total += record.PracticeDuration
	}
	return total
}

// calculateAverageFeeling 计算平均感受评分
func (s *checkinService) calculateAverageFeeling(records []*models.CheckinRecords) float64 {
	if len(records) == 0 {
		return 0
	}

	total := 0
	for _, record := range records {
		total += int(record.FeelingScore)
	}

	return float64(total) / float64(len(records))
}

// calculateThisWeekCheckins 计算本周打卡次数
func (s *checkinService) calculateThisWeekCheckins(records []*models.CheckinRecords) int {
	now := time.Now()
	weekStart := now.AddDate(0, 0, -int(now.Weekday()))

	count := 0
	for _, record := range records {
		if record.CheckinDate.After(weekStart) {
			count++
		}
	}

	return count
}

// calculateThisMonthCheckins 计算本月打卡次数
func (s *checkinService) calculateThisMonthCheckins(records []*models.CheckinRecords) int {
	now := time.Now()
	monthStart := time.Date(now.Year(), now.Month(), 1, 0, 0, 0, 0, now.Location())

	count := 0
	for _, record := range records {
		if record.CheckinDate.After(monthStart) {
			count++
		}
	}

	return count
}

// ==================== 新增方法：训练营打卡日历 ====================

// GetCampCheckinCalendar 获取训练营打卡日历
func (s *checkinService) GetCampCheckinCalendar(childID uint, campID uint) (*dto.CampCheckinCalendarResponse, error) {
	// 1. 获取参与记录
	participation, err := s.userCampParticipationRepo.GetByCampAndChild(campID, childID)
	if err != nil {
		if err == errcode.ErrDataNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("训练营参与记录不存在")
		}
		logger.Error("获取训练营参与记录失败", "error", err, "childID", childID, "campID", campID)
		return nil, errcode.ErrInternalServer
	}

	// 2. 获取训练营信息
	camp, err := s.trainingCampsRepo.GetByID(campID)
	if err != nil {
		if err == errcode.ErrDataNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("训练营不存在")
		}
		logger.Error("获取训练营信息失败", "error", err, "campID", campID)
		return nil, errcode.ErrInternalServer
	}

	// 3. 获取打卡日期列表
	checkinDates, err := s.campCheckinDatesRepo.GetByParticipationID(int64(participation.ID))
	if err != nil {
		logger.Error("获取打卡日期列表失败", "error", err, "participationID", participation.ID)
		return nil, errcode.ErrInternalServer
	}

	// 4. 如果没有打卡日期数据，初始化
	if len(checkinDates) == 0 {
		err = s.InitializeCampCheckinDates(int64(participation.ID), campID, participation.ParticipationDate, camp.DurationDays)
		if err != nil {
			logger.Error("初始化打卡日期失败", "error", err, "participationID", participation.ID)
			return nil, errcode.ErrInternalServer
		}

		// 重新获取打卡日期列表
		checkinDates, err = s.campCheckinDatesRepo.GetByParticipationID(int64(participation.ID))
		if err != nil {
			logger.Error("重新获取打卡日期列表失败", "error", err, "participationID", participation.ID)
			return nil, errcode.ErrInternalServer
		}
	}

	// 5. 获取打卡记录详情
	checkinRecords, _, err := s.checkinRecordsRepo.GetByChildIDAndCampID(childID, campID, 0, 1000)
	if err != nil && err != errcode.ErrDataNotFound {
		logger.Error("获取打卡记录失败", "error", err, "childID", childID, "campID", campID)
		return nil, errcode.ErrInternalServer
	}

	// 6. 构建响应数据
	var recordsList []*models.CheckinRecords
	if checkinRecords != nil {
		recordsList = checkinRecords
	}
	return s.buildCampCheckinCalendarResponse(camp, participation, checkinDates, recordsList), nil
}

// InitializeCampCheckinDates 初始化训练营打卡日期
func (s *checkinService) InitializeCampCheckinDates(participationID int64, campID uint, startDate time.Time, totalDays int) error {
	// 生成打卡日期列表
	checkinDatesList := make([]*models.CampCheckinDates, 0, totalDays)

	for i := 0; i < totalDays; i++ {
		checkinDate := startDate.AddDate(0, 0, i)

		checkinDateRecord := &models.CampCheckinDates{
			ParticipationID: participationID,
			DayNumber:       i + 1,
			CheckinDate:     checkinDate,
			DateType:        models.DateTypeNormal, // 暂时都设为正常打卡日
			Status:          models.CheckinStatusPending,
			IsMakeupAllowed: 1,
		}

		checkinDatesList = append(checkinDatesList, checkinDateRecord)
	}

	// 批量创建
	return s.campCheckinDatesRepo.BatchCreate(checkinDatesList)
}

// buildCampCheckinCalendarResponse 构建训练营打卡日历响应
func (s *checkinService) buildCampCheckinCalendarResponse(
	camp *models.TrainingCamps,
	participation *models.UserCampParticipations,
	checkinDates []*models.CampCheckinDates,
	checkinRecords []*models.CheckinRecords,
) *dto.CampCheckinCalendarResponse {
	// 构建打卡记录映射
	checkinRecordMap := make(map[string]*models.CheckinRecords)
	for _, record := range checkinRecords {
		dateKey := record.CheckinDate.Format("2006-01-02")
		checkinRecordMap[dateKey] = record
	}

	// 构建日历日期信息
	dates := make([]*dto.CalendarDateInfo, 0, len(checkinDates))
	currentDay := 1

	for _, dateInfo := range checkinDates {
		dateKey := dateInfo.CheckinDate.Format("2006-01-02")

		// 获取对应的打卡记录
		var checkinData *models.CheckinRecordsResponse
		if record, exists := checkinRecordMap[dateKey]; exists {
			checkinData = record.ToResponse()
		}

		// 构建日期信息
		calendarDate := &dto.CalendarDateInfo{
			Date:        dateKey,
			DayNumber:   dateInfo.DayNumber,
			Status:      dateInfo.GetStatusText(),
			CanMakeup:   dateInfo.CanMakeup(),
			CheckinData: checkinData,
		}

		dates = append(dates, calendarDate)

		// 更新当前天数（找到今天或最近的未完成日期）
		if dateInfo.IsToday() || (dateInfo.Status == models.CheckinStatusPending && dateInfo.CheckinDate.Before(time.Now())) {
			currentDay = dateInfo.DayNumber
		}
	}

	// 计算开始和结束日期
	var startDate, endDate string
	if len(checkinDates) > 0 {
		startDate = checkinDates[0].CheckinDate.Format("2006-01-02")
		endDate = checkinDates[len(checkinDates)-1].CheckinDate.Format("2006-01-02")
	}

	return &dto.CampCheckinCalendarResponse{
		CampInfo: &dto.CampInfo{
			ID:        camp.ID,
			Title:     camp.Title,
			Subtitle:  camp.Subtitle,
			TotalDays: camp.DurationDays,
		},
		CalendarData: &dto.CalendarData{
			TotalDays:  camp.DurationDays,
			CurrentDay: currentDay,
			StartDate:  startDate,
			EndDate:    endDate,
			Dates:      dates,
		},
		MakeupInfo: &dto.MakeupInfo{
			TotalCount:     int(participation.MakeupTotalCount),
			UsedCount:      int(participation.MakeupUsedCount),
			AvailableCount: int(participation.MakeupTotalCount - participation.MakeupUsedCount),
		},
	}
}
