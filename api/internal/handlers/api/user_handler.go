package api

import (
	"net/http"
	"strconv"

	"kids-platform/internal/models"
	"kids-platform/internal/services/api"
	"kids-platform/pkg/logger"
	"kids-platform/pkg/response"

	"github.com/gin-gonic/gin"
)

// UserHandler 用户处理器
type UserHandler struct {
	userService api.UserService
}

// NewUserHandler 创建用户处理器
func NewUserHandler(userService api.UserService) *UserHandler {
	return &UserHandler{
		userService: userService,
	}
}

// WechatLogin 微信登录
// @Summary 微信登录
// @Description 通过微信授权码进行登录，支持静默登录和获取用户信息登录
// @Tags 用户认证
// @Accept json
// @Produce json
// @Param request body models.WechatLoginRequest true "微信登录请求"
// @Success 200 {object} response.Response{data=models.WechatLoginResponse} "登录成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "微信授权失败"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /auth/wechat/login [post]
func (h *UserHandler) WechatLogin(c *gin.Context) {
	var req models.WechatLoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid WeChat login request", "error", err)
		response.Error(c, http.StatusBadRequest, "请求参数错误", err.Error())
		return
	}

	// 调用用户服务进行微信登录
	result, err := h.userService.WechatLogin(&req)
	if err != nil {
		logger.Error("WeChat login failed", "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	logger.Info("WeChat login successful", "user_id", result.UserInfo.ID, "is_new_user", result.IsNewUser)
	response.Success(c, result)
}

// GetUserInfo 获取用户信息
// @Summary 获取用户信息
// @Description 获取当前登录用户的详细信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Success 200 {object} response.Response{data=models.UsersResponse} "获取成功"
// @Failure 401 {object} response.Response "未授权"
// @Failure 404 {object} response.Response "用户不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /user/info [get]
func (h *UserHandler) GetUserInfo(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.Error("User ID not found in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID不存在")
		return
	}

	uid, ok := userID.(uint64)
	if !ok {
		logger.Error("Invalid user ID type in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID类型错误")
		return
	}

	// 获取用户信息
	userInfo, err := h.userService.GetUserByID(uint(uid))
	if err != nil {
		logger.Error("Failed to get user info", "user_id", uid, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, userInfo)
}

// UpdateUserInfo 更新用户信息
// @Summary 更新用户信息
// @Description 更新当前登录用户的个人信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param request body models.UsersUpdateRequest true "用户更新请求"
// @Success 200 {object} response.Response{data=models.UsersResponse} "更新成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 404 {object} response.Response "用户不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /user/info [put]
func (h *UserHandler) UpdateUserInfo(c *gin.Context) {
	// 从JWT中获取用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		logger.Error("User ID not found in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID不存在")
		return
	}

	uid, ok := userID.(uint64)
	if !ok {
		logger.Error("Invalid user ID type in context")
		response.Error(c, http.StatusUnauthorized, "未授权", "用户ID类型错误")
		return
	}

	var req models.UsersUpdateRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logger.Error("Invalid user update request", "error", err)
		response.Error(c, http.StatusBadRequest, "请求参数错误", err.Error())
		return
	}

	// 更新用户信息
	userInfo, err := h.userService.UpdateUser(uint(uid), &req)
	if err != nil {
		logger.Error("Failed to update user info", "user_id", uid, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	logger.Info("User info updated successfully", "user_id", uid)
	response.Success(c, userInfo)
}

// GetUserByID 根据ID获取用户信息（管理员接口）
// @Summary 根据ID获取用户信息
// @Description 管理员根据用户ID获取用户详细信息
// @Tags 用户管理
// @Accept json
// @Produce json
// @Security BearerAuth
// @Param id path int true "用户ID"
// @Success 200 {object} response.Response{data=models.UsersResponse} "获取成功"
// @Failure 400 {object} response.Response "请求参数错误"
// @Failure 401 {object} response.Response "未授权"
// @Failure 403 {object} response.Response "权限不足"
// @Failure 404 {object} response.Response "用户不存在"
// @Failure 500 {object} response.Response "服务器内部错误"
// @Router /admin/user/{id} [get]
func (h *UserHandler) GetUserByID(c *gin.Context) {
	// 获取路径参数中的用户ID
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		logger.Error("Invalid user ID parameter", "id", idStr, "error", err)
		response.Error(c, http.StatusBadRequest, "请求参数错误", "用户ID格式错误")
		return
	}

	// 获取用户信息
	userInfo, err := h.userService.GetUserByID(uint(id))
	if err != nil {
		logger.Error("Failed to get user by ID", "user_id", id, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, userInfo)
}
