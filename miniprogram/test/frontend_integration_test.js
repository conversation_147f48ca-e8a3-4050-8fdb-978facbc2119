// 前端集成测试脚本
// 用于在微信开发者工具中测试API集成功能

/**
 * 测试工具函数
 */
const TestUtils = {
  /**
   * 模拟页面实例
   */
  createMockPage() {
    return {
      data: {
        campId: 1,
        childId: 1,
        loading: false,
        campInfo: {},
        currentWeek: [],
        currentMonthDays: [],
        makeupInfo: {}
      },
      setData(data) {
        Object.assign(this.data, data);
        console.log('页面数据更新:', data);
      }
    };
  },

  /**
   * 验证API响应数据格式
   */
  validateApiResponse(response) {
    const errors = [];
    
    if (!response) {
      errors.push('响应数据为空');
      return errors;
    }

    if (!response.data) {
      errors.push('缺少data字段');
      return errors;
    }

    const { camp_info, calendar_data, makeup_info } = response.data;

    // 验证训练营信息
    if (!camp_info) {
      errors.push('缺少camp_info字段');
    } else {
      if (!camp_info.id) errors.push('camp_info缺少id字段');
      if (!camp_info.title) errors.push('camp_info缺少title字段');
      if (!camp_info.total_days) errors.push('camp_info缺少total_days字段');
    }

    // 验证日历数据
    if (!calendar_data) {
      errors.push('缺少calendar_data字段');
    } else {
      if (!calendar_data.dates) {
        errors.push('calendar_data缺少dates字段');
      } else if (!Array.isArray(calendar_data.dates)) {
        errors.push('calendar_data.dates不是数组');
      } else if (calendar_data.dates.length === 0) {
        errors.push('calendar_data.dates为空数组');
      } else {
        // 验证第一个日期数据的格式
        const firstDate = calendar_data.dates[0];
        if (!firstDate.date) errors.push('日期数据缺少date字段');
        if (!firstDate.status) errors.push('日期数据缺少status字段');
        if (typeof firstDate.day_number !== 'number') errors.push('日期数据缺少day_number字段');
      }
    }

    // 验证补卡信息
    if (!makeup_info) {
      errors.push('缺少makeup_info字段');
    } else {
      if (typeof makeup_info.total_count !== 'number') errors.push('makeup_info缺少total_count字段');
      if (typeof makeup_info.used_count !== 'number') errors.push('makeup_info缺少used_count字段');
      if (typeof makeup_info.available_count !== 'number') errors.push('makeup_info缺少available_count字段');
    }

    return errors;
  },

  /**
   * 验证状态映射
   */
  validateStatusMapping() {
    const validStatuses = ['pending', 'completed', 'makeup', 'missed', 'skipped'];
    const testStatuses = ['pending', 'completed', 'makeup', 'missed'];
    
    console.log('测试状态映射...');
    
    testStatuses.forEach(status => {
      if (!validStatuses.includes(status)) {
        console.error(`无效状态: ${status}`);
      } else {
        console.log(`✓ 状态 ${status} 有效`);
      }
    });
  },

  /**
   * 验证日历数据生成
   */
  validateCalendarGeneration(dates) {
    console.log('验证日历数据生成...');
    
    if (!Array.isArray(dates) || dates.length === 0) {
      console.error('日历数据为空或格式错误');
      return false;
    }

    // 检查日期连续性
    const sortedDates = dates.sort((a, b) => new Date(a.date) - new Date(b.date));
    console.log(`日历包含 ${sortedDates.length} 天数据`);
    console.log(`日期范围: ${sortedDates[0].date} 到 ${sortedDates[sortedDates.length - 1].date}`);

    // 检查状态分布
    const statusCount = {};
    sortedDates.forEach(dateInfo => {
      statusCount[dateInfo.status] = (statusCount[dateInfo.status] || 0) + 1;
    });
    
    console.log('状态分布:', statusCount);
    return true;
  }
};

/**
 * 主测试函数
 */
async function runFrontendIntegrationTest() {
  console.log('=== 开始前端集成测试 ===');
  
  try {
    // 1. 测试API模块导入
    console.log('1. 测试API模块导入...');
    const checkinAPI = require('../apis/checkin.js');
    if (!checkinAPI.getCampCheckinCalendar) {
      throw new Error('API模块导入失败');
    }
    console.log('✓ API模块导入成功');

    // 2. 测试API调用
    console.log('2. 测试API调用...');
    const response = await checkinAPI.getCampCheckinCalendar(1, 1);
    console.log('✓ API调用成功');

    // 3. 验证响应数据格式
    console.log('3. 验证响应数据格式...');
    const validationErrors = TestUtils.validateApiResponse(response);
    if (validationErrors.length > 0) {
      console.error('数据格式验证失败:', validationErrors);
      return false;
    }
    console.log('✓ 数据格式验证通过');

    // 4. 测试状态映射
    console.log('4. 测试状态映射...');
    TestUtils.validateStatusMapping();

    // 5. 验证日历数据
    console.log('5. 验证日历数据...');
    const calendarData = response.data.calendar_data;
    if (!TestUtils.validateCalendarGeneration(calendarData.dates)) {
      return false;
    }
    console.log('✓ 日历数据验证通过');

    // 6. 模拟页面数据处理
    console.log('6. 模拟页面数据处理...');
    const mockPage = TestUtils.createMockPage();
    
    // 模拟processCampCheckinCalendar方法的核心逻辑
    const { camp_info, makeup_info } = response.data;
    
    mockPage.setData({
      campInfo: {
        id: camp_info.id,
        title: camp_info.title,
        subtitle: camp_info.subtitle,
        totalDays: camp_info.total_days,
        currentDay: calendarData.current_day
      },
      makeupInfo: {
        totalCount: makeup_info.total_count,
        usedCount: makeup_info.used_count,
        availableCount: makeup_info.available_count
      }
    });
    
    console.log('✓ 页面数据处理成功');

    console.log('=== 前端集成测试完成 ===');
    console.log('✅ 所有测试通过！');
    return true;

  } catch (error) {
    console.error('❌ 测试失败:', error);
    console.error('错误详情:', error.message);
    return false;
  }
}

/**
 * 快速测试函数（用于开发者工具控制台）
 */
function quickTest() {
  console.log('执行快速测试...');
  runFrontendIntegrationTest().then(success => {
    if (success) {
      console.log('🎉 快速测试通过！');
    } else {
      console.log('❌ 快速测试失败！');
    }
  });
}

// 导出测试函数
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    runFrontendIntegrationTest,
    quickTest,
    TestUtils
  };
}

// 在微信开发者工具中可以直接调用
if (typeof wx !== 'undefined') {
  console.log('前端集成测试脚本已加载');
  console.log('在控制台中输入 quickTest() 开始测试');
}
