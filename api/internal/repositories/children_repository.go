package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// ChildrenRepository 孩子档案仓储接口
type ChildrenRepository interface {
	Create(child *models.Children) error
	GetByID(id uint) (*models.Children, error)
	Update(id uint, child *models.Children) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.Children, int64, error)
	GetByUserID(userID uint) ([]*models.Children, error)
	GetCurrentChildByUserID(userID uint) (*models.Children, error)
}

// childrenRepository 孩子档案仓储实现
type childrenRepository struct {
	db *gorm.DB
}

// NewChildrenRepository 创建孩子档案仓储
func NewChildrenRepository(db *gorm.DB) ChildrenRepository {
	return &childrenRepository{
		db: db,
	}
}

// Create 创建孩子档案
func (r *childrenRepository) Create(child *models.Children) error {
	if err := r.db.Create(child).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建孩子档案失败", err.Error())
	}
	return nil
}

// GetByID 根据ID获取孩子档案（自动过滤软删除）
func (r *childrenRepository) GetByID(id uint) (*models.Children, error) {
	var child models.Children
	if err := r.db.Where("deleted_at IS NULL").First(&child, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("孩子档案不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询孩子档案失败", err.Error())
	}
	return &child, nil
}

// Update 更新孩子档案
func (r *childrenRepository) Update(id uint, child *models.Children) error {
	if err := r.db.Model(&models.Children{}).Where("id = ? AND deleted_at IS NULL", id).Updates(child).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新孩子档案失败", err.Error())
	}
	return nil
}

// Delete 软删除孩子档案
func (r *childrenRepository) Delete(id uint) error {
	if err := r.db.Model(&models.Children{}).Where("id = ?", id).Update("deleted_at", "NOW()").Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除孩子档案失败", err.Error())
	}
	return nil
}

// List 获取孩子档案列表（自动过滤软删除）
func (r *childrenRepository) List(offset, limit int) ([]*models.Children, int64, error) {
	var children []*models.Children
	var total int64

	// 获取总数（过滤软删除）
	if err := r.db.Model(&models.Children{}).Where("deleted_at IS NULL").Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询孩子档案总数失败", err.Error())
	}

	// 获取列表（过滤软删除）
	if err := r.db.Where("deleted_at IS NULL").Offset(offset).Limit(limit).Find(&children).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询孩子档案列表失败", err.Error())
	}

	return children, total, nil
}

// GetByUserID 获取用户管理的所有孩子（通过关联表查询，自动过滤软删除）
func (r *childrenRepository) GetByUserID(userID uint) ([]*models.Children, error) {
	var children []*models.Children

	err := r.db.Table("children c").
		Select("c.*").
		Joins("INNER JOIN user_children uc ON c.id = uc.child_id").
		Where("uc.user_id = ? AND c.deleted_at IS NULL AND uc.deleted_at IS NULL", userID).
		Order("uc.created_at DESC").
		Find(&children).Error

	if err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询用户孩子列表失败", err.Error())
	}

	return children, nil
}

// GetCurrentChildByUserID 获取用户当前选择的孩子
func (r *childrenRepository) GetCurrentChildByUserID(userID uint) (*models.Children, error) {
	var child models.Children

	err := r.db.Table("children c").
		Select("c.*").
		Joins("INNER JOIN users u ON c.id = u.current_child_id").
		Where("u.id = ? AND c.deleted_at IS NULL AND u.deleted_at IS NULL", userID).
		First(&child).Error

	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("当前孩子不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询当前孩子失败", err.Error())
	}

	return &child, nil
}
