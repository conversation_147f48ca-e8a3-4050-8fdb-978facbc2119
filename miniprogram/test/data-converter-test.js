// 数据转换器测试文件
// 用于验证前后端数据转换是否正确

const dataConverter = require('../utils/data-converter.js');

// 模拟后端返回的孩子数据
const mockBackendChildData = {
  id: 1,
  name: "小明",
  nickname: "明明",
  gender: 1, // 1=男孩
  birth_date: "2015-06-15T00:00:00Z",
  age: 8,
  school: "实验小学",
  grade: "三年级2班",
  province: "北京市",
  city: "海淀区",
  avatar: "👦",
  skill_level: 2,
  best_score_1min: 50,
  best_score_continuous: 120,
  preferred_difficulty: 2,
  learning_goals: "提高跳绳技能",
  privacy_level: 1,
  show_in_leaderboard: 1,
  total_checkins: 25,
  total_points: 1250,
  continuous_days: 7,
  last_checkin_date: "2025-07-30T00:00:00Z",
  status: 1,
  created_at: "2025-01-01T00:00:00Z",
  updated_at: "2025-07-31T00:00:00Z"
};

// 测试数据转换
console.log('=== 数据转换测试 ===');
console.log('原始后端数据:', mockBackendChildData);

const frontendData = dataConverter.convertChildToFrontend(mockBackendChildData);
console.log('转换后前端数据:', frontendData);

// 验证关键字段
console.log('\n=== 关键字段验证 ===');
console.log('性别转换:', mockBackendChildData.gender, '->', frontendData.gender);
console.log('生日转换:', mockBackendChildData.birth_date, '->', frontendData.birthday);
console.log('年龄:', frontendData.age);
console.log('总打卡数:', mockBackendChildData.total_checkins, '->', frontendData.totalCheckins);
console.log('总积分:', mockBackendChildData.total_points, '->', frontendData.totalPoints);
console.log('连续天数:', mockBackendChildData.continuous_days, '->', frontendData.currentStreak);
console.log('技能等级:', mockBackendChildData.skill_level, '->', frontendData.skillLevel);
console.log('最佳成绩1分钟:', mockBackendChildData.best_score_1min, '->', frontendData.bestScore1Min);

// 验证字段清理
console.log('\n=== 字段清理验证 ===');
console.log('是否清理了birth_date:', !frontendData.hasOwnProperty('birth_date'));
console.log('是否清理了total_checkins:', !frontendData.hasOwnProperty('total_checkins'));
console.log('是否清理了total_points:', !frontendData.hasOwnProperty('total_points'));
console.log('是否清理了continuous_days:', !frontendData.hasOwnProperty('continuous_days'));

// 测试批量转换
console.log('\n=== 批量转换测试 ===');
const mockBackendList = [mockBackendChildData, {
  ...mockBackendChildData,
  id: 2,
  name: "小红",
  gender: 2, // 2=女孩
  total_checkins: 30,
  total_points: 1500,
  continuous_days: 10
}];

const frontendList = dataConverter.convertChildrenListToFrontend(mockBackendList);
console.log('批量转换结果:', frontendList.map(child => ({
  id: child.id,
  name: child.name,
  gender: child.gender,
  totalCheckins: child.totalCheckins,
  totalPoints: child.totalPoints,
  currentStreak: child.currentStreak
})));

console.log('\n=== 测试完成 ===');
