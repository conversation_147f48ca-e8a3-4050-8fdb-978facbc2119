package main

import (
	"fmt"
	"log"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
)

func main() {
	// 数据库连接配置
	dsn := "admin:235lwx123456@tcp(*************:3306)/hop_planet_dev?charset=utf8mb4&parseTime=True&loc=Local"
	
	db, err := gorm.Open(mysql.Open(dsn), &gorm.Config{})
	if err != nil {
		log.Fatal("Failed to connect to database:", err)
	}

	fmt.Println("=== 插入测试数据 ===")

	// 插入用户参与记录
	fmt.Println("Inserting user camp participation...")
	participationSQL := `
	INSERT INTO user_camp_participations (
		id, camp_id, user_id, child_id, participation_status, participation_date,
		current_day, progress_percentage, total_checkins, consecutive_days,
		total_study_minutes, rating, review_text,
		camp_title, camp_subtitle, total_checkin_days, completed_checkin_days,
		makeup_used_count, makeup_total_count,
		created_at, updated_at
	) VALUES (
		1, 1, 1, 1, 1, '2024-01-15',
		14, 66.67, 12, 10,
		420, 0, NULL,
		'21天跳绳挑战', '从零基础到连续跳绳300个', 21, 12,
		1, 3,
		'2024-01-15 08:00:00', '2024-01-28 20:30:00'
	) ON DUPLICATE KEY UPDATE 
		camp_title = VALUES(camp_title),
		camp_subtitle = VALUES(camp_subtitle),
		total_checkin_days = VALUES(total_checkin_days),
		completed_checkin_days = VALUES(completed_checkin_days),
		makeup_used_count = VALUES(makeup_used_count),
		makeup_total_count = VALUES(makeup_total_count);`
	
	err = db.Exec(participationSQL).Error
	if err != nil {
		log.Printf("Failed to insert participation: %v", err)
	} else {
		fmt.Println("User camp participation inserted successfully!")
	}

	// 插入打卡日期数据
	fmt.Println("Inserting checkin dates...")
	checkinDatesSQL := `
	INSERT INTO camp_checkin_dates (
		participation_id, day_number, checkin_date, status, date_type,
		created_at, updated_at
	) VALUES 
	(1, 1, '2024-01-15', 2, 1, NOW(), NOW()),
	(1, 2, '2024-01-16', 2, 1, NOW(), NOW()),
	(1, 3, '2024-01-17', 2, 1, NOW(), NOW()),
	(1, 4, '2024-01-18', 2, 1, NOW(), NOW()),
	(1, 5, '2024-01-19', 2, 1, NOW(), NOW()),
	(1, 6, '2024-01-20', 2, 2, NOW(), NOW()),
	(1, 7, '2024-01-21', 2, 2, NOW(), NOW()),
	(1, 8, '2024-01-22', 2, 1, NOW(), NOW()),
	(1, 9, '2024-01-23', 2, 1, NOW(), NOW()),
	(1, 10, '2024-01-24', 2, 1, NOW(), NOW()),
	(1, 11, '2024-01-25', 2, 1, NOW(), NOW()),
	(1, 12, '2024-01-26', 2, 1, NOW(), NOW()),
	(1, 13, '2024-01-27', 2, 2, NOW(), NOW()),
	(1, 14, '2024-01-28', 1, 2, NOW(), NOW()),
	(1, 15, '2024-01-29', 1, 1, NOW(), NOW()),
	(1, 16, '2024-01-30', 1, 1, NOW(), NOW()),
	(1, 17, '2024-01-31', 1, 1, NOW(), NOW()),
	(1, 18, '2024-02-01', 1, 1, NOW(), NOW()),
	(1, 19, '2024-02-02', 1, 1, NOW(), NOW()),
	(1, 20, '2024-02-03', 1, 2, NOW(), NOW()),
	(1, 21, '2024-02-04', 1, 2, NOW(), NOW())
	ON DUPLICATE KEY UPDATE 
		status = VALUES(status),
		date_type = VALUES(date_type);`
	
	err = db.Exec(checkinDatesSQL).Error
	if err != nil {
		log.Printf("Failed to insert checkin dates: %v", err)
	} else {
		fmt.Println("Checkin dates inserted successfully!")
	}

	// 验证数据
	fmt.Println("=== 验证插入的数据 ===")
	
	var participationCount int64
	err = db.Raw("SELECT COUNT(*) FROM user_camp_participations WHERE camp_id = 1 AND child_id = 1").Scan(&participationCount).Error
	if err != nil {
		log.Printf("Error checking user_camp_participations: %v", err)
	} else {
		fmt.Printf("User camp participations (camp_id=1, child_id=1): %d\n", participationCount)
	}

	var checkinDatesCount int64
	err = db.Raw("SELECT COUNT(*) FROM camp_checkin_dates WHERE participation_id = 1").Scan(&checkinDatesCount).Error
	if err != nil {
		log.Printf("Error checking camp_checkin_dates: %v", err)
	} else {
		fmt.Printf("Camp checkin dates for participation_id=1: %d\n", checkinDatesCount)
	}

	fmt.Println("=== 测试数据插入完成 ===")
}
