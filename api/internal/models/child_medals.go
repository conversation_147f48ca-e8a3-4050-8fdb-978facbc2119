package models

import (
	"time"

)

// ==================== Model ====================

// ChildMedals 孩子勋章记录表：记录每个孩子的勋章获得情况
type ChildMedals struct {
	BaseModel
	ChildID int64 `json:"child_id" gorm:"column:child_id;not null;default:0" validate:"required"` // 孩子ID，关联children.id
	MedalID int `json:"medal_id" gorm:"column:medal_id;not null;default:0" validate:"required"` // 勋章ID，关联medals.id
	IsUnlocked int8 `json:"is_unlocked" gorm:"column:is_unlocked;not null;default:0" validate:"required"` // 是否已解锁 0:否 1:是
	CurrentProgress int `json:"current_progress" gorm:"column:current_progress;not null;default:0" validate:"required"` // 当前进度
	TargetProgress int `json:"target_progress" gorm:"column:target_progress;not null;default:0" validate:"required"` // 目标进度
	UnlockedAt time.Time `json:"unlocked_at" gorm:"column:unlocked_at"` // 解锁时间，NULL表示未解锁
	PointsEarned int `json:"points_earned" gorm:"column:points_earned;not null;default:0" validate:"required"` // 获得积分
}

// TableName 指定表名
func (ChildMedals) TableName() string {
	return "child_medals"
}

// ==================== Requests ====================

// ChildMedalsCreateRequest 创建孩子勋章记录表：记录每个孩子的勋章获得情况请求
type ChildMedalsCreateRequest struct {
	ChildID int64 `json:"child_id" binding:"required" validate:"required"` // 孩子ID，关联children.id
	MedalID int `json:"medal_id" binding:"required" validate:"required"` // 勋章ID，关联medals.id
	IsUnlocked int8 `json:"is_unlocked" binding:"required" validate:"required"` // 是否已解锁 0:否 1:是
	CurrentProgress int `json:"current_progress" binding:"required" validate:"required"` // 当前进度
	TargetProgress int `json:"target_progress" binding:"required" validate:"required"` // 目标进度
	UnlockedAt time.Time `json:"unlocked_at"` // 解锁时间，NULL表示未解锁
	PointsEarned int `json:"points_earned" binding:"required" validate:"required"` // 获得积分
}

// ChildMedalsUpdateRequest 更新孩子勋章记录表：记录每个孩子的勋章获得情况请求
type ChildMedalsUpdateRequest struct {
	ChildID *int64 `json:"child_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 孩子ID，关联children.id
	MedalID *int `json:"medal_id,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 勋章ID，关联medals.id
	IsUnlocked *int8 `json:"is_unlocked,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 是否已解锁 0:否 1:是
	CurrentProgress *int `json:"current_progress,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 当前进度
	TargetProgress *int `json:"target_progress,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 目标进度
	UnlockedAt *time.Time `json:"unlocked_at,omitempty"` // 解锁时间，NULL表示未解锁
	PointsEarned *int `json:"points_earned,omitempty" binding:"omitempty,required" validate:"omitempty,required"` // 获得积分
}

// ==================== Responses ====================

// ChildMedalsResponse 孩子勋章记录表：记录每个孩子的勋章获得情况响应
type ChildMedalsResponse struct {
	ID uint `json:"id"` // 主键ID
	ChildID int64 `json:"child_id"` // 孩子ID，关联children.id
	MedalID int `json:"medal_id"` // 勋章ID，关联medals.id
	IsUnlocked int8 `json:"is_unlocked"` // 是否已解锁 0:否 1:是
	CurrentProgress int `json:"current_progress"` // 当前进度
	TargetProgress int `json:"target_progress"` // 目标进度
	UnlockedAt time.Time `json:"unlocked_at"` // 解锁时间，NULL表示未解锁
	PointsEarned int `json:"points_earned"` // 获得积分
	CreatedAt time.Time `json:"created_at"` // 创建时间
	UpdatedAt time.Time `json:"updated_at"` // 更新时间
}

// ChildMedalsListResponse 孩子勋章记录表：记录每个孩子的勋章获得情况列表响应
type ChildMedalsListResponse struct {
	List  []*ChildMedalsResponse `json:"list"`  // 孩子勋章记录表：记录每个孩子的勋章获得情况列表
	Total int64                  `json:"total"` // 总数
}

// ==================== Converters ====================

// ToResponse 将孩子勋章记录表：记录每个孩子的勋章获得情况模型转换为响应结构
func (m *ChildMedals) ToResponse() *ChildMedalsResponse {
	return &ChildMedalsResponse{
		ID: m.ID,
		ChildID: m.ChildID,
		MedalID: m.MedalID,
		IsUnlocked: m.IsUnlocked,
		CurrentProgress: m.CurrentProgress,
		TargetProgress: m.TargetProgress,
		UnlockedAt: m.UnlockedAt,
		PointsEarned: m.PointsEarned,
		CreatedAt: m.CreatedAt,
		UpdatedAt: m.UpdatedAt,
	}
}

// ToResponseList 将孩子勋章记录表：记录每个孩子的勋章获得情况模型列表转换为响应列表
func ChildMedalsToResponseList(models []*ChildMedals, total int64) *ChildMedalsListResponse {
	list := make([]*ChildMedalsResponse, 0, len(models))
	for _, model := range models {
		list = append(list, model.ToResponse())
	}
	
	return &ChildMedalsListResponse{
		List:  list,
		Total: total,
	}
}

// ApplyUpdateRequest 应用更新请求到孩子勋章记录表：记录每个孩子的勋章获得情况模型
func (m *ChildMedals) ApplyUpdateRequest(req *ChildMedalsUpdateRequest) {
	if req.ChildID != nil {
		m.ChildID = *req.ChildID
	}
	if req.MedalID != nil {
		m.MedalID = *req.MedalID
	}
	if req.IsUnlocked != nil {
		m.IsUnlocked = *req.IsUnlocked
	}
	if req.CurrentProgress != nil {
		m.CurrentProgress = *req.CurrentProgress
	}
	if req.TargetProgress != nil {
		m.TargetProgress = *req.TargetProgress
	}
	if req.UnlockedAt != nil {
		m.UnlockedAt = *req.UnlockedAt
	}
	if req.PointsEarned != nil {
		m.PointsEarned = *req.PointsEarned
	}
}
