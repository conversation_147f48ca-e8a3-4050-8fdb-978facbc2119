package api

import (
	"kids-platform/internal/models"
	"kids-platform/internal/repositories"
	"kids-platform/pkg/errcode"
	"kids-platform/pkg/logger"
	"time"
)

// PointsService 积分服务接口
type PointsService interface {
	// 添加积分
	AddPoints(childID uint, sourceType int8, sourceID uint, points int, description string) error
	// 获取积分统计
	GetPointsStats(childID uint) (*PointsStatsResponse, error)
	// 获取积分历史
	GetPointsHistory(childID uint, offset, limit int) (*PointsHistoryResponse, error)
	// 获取排行榜
	GetLeaderboard(limit int) (*LeaderboardResponse, error)
	// 更新周排名
	UpdateWeeklyRanking() error
}

// pointsService 积分服务实现
type pointsService struct {
	childPointsRepo  repositories.ChildPointsRepository
	pointRecordsRepo repositories.PointRecordsRepository
}

// NewPointsService 创建积分服务
func NewPointsService(
	childPointsRepo repositories.ChildPointsRepository,
	pointRecordsRepo repositories.PointRecordsRepository,
) PointsService {
	return &pointsService{
		childPointsRepo:  childPointsRepo,
		pointRecordsRepo: pointRecordsRepo,
	}
}

// ==================== 响应结构定义 ====================

// PointsStatsResponse 积分统计响应
type PointsStatsResponse struct {
	TotalPoints       int64     `json:"total_points"`        // 总积分
	WeekPoints        int       `json:"week_points"`         // 本周积分
	MonthPoints       int       `json:"month_points"`        // 本月积分
	TotalCheckins     int       `json:"total_checkins"`      // 总打卡次数
	ContinuousDays    int       `json:"continuous_days"`     // 连续打卡天数
	MaxContinuousDays int       `json:"max_continuous_days"` // 最大连续天数
	WeekRank          int       `json:"week_rank"`           // 本周排名
	LastWeekRank      int       `json:"last_week_rank"`      // 上周排名
	LastCheckinDate   time.Time `json:"last_checkin_date"`   // 最后打卡日期
	CurrentLevel      int       `json:"current_level"`       // 当前等级
	NextLevelPoints   int64     `json:"next_level_points"`   // 下一等级所需积分
}

// PointsHistoryResponse 积分历史响应
type PointsHistoryResponse struct {
	List  []*models.PointRecordsResponse `json:"list"`  // 积分记录列表
	Total int64                          `json:"total"` // 总数
}

// LeaderboardResponse 排行榜响应
type LeaderboardResponse struct {
	WeeklyRanking  []*LeaderboardItem `json:"weekly_ranking"`  // 周排行榜
	MonthlyRanking []*LeaderboardItem `json:"monthly_ranking"` // 月排行榜
}

// LeaderboardItem 排行榜项目
type LeaderboardItem struct {
	ChildID        uint   `json:"child_id"`        // 孩子ID
	ChildName      string `json:"child_name"`      // 孩子姓名
	Points         int64  `json:"points"`          // 积分
	Rank           int    `json:"rank"`            // 排名
	ContinuousDays int    `json:"continuous_days"` // 连续天数
}

// ==================== 服务方法实现 ====================

// AddPoints 添加积分
func (s *pointsService) AddPoints(childID uint, sourceType int8, sourceID uint, points int, description string) error {
	// 获取或创建孩子积分记录
	childPoints, err := s.childPointsRepo.GetByChildID(childID)
	if err != nil && err != errcode.ErrDataNotFound {
		logger.Error("Failed to get child points", "child_id", childID, "error", err)
		return errcode.ErrDatabase.WithDetails("获取孩子积分记录失败")
	}

	// 如果不存在则创建
	if childPoints == nil {
		childPoints = &models.ChildPoints{
			ChildID:           int64(childID),
			TotalPoints:       0,
			WeekPoints:        0,
			MonthPoints:       0,
			TotalCheckins:     0,
			ContinuousDays:    0,
			MaxContinuousDays: 0,
			WeekRank:          0,
			LastWeekRank:      0,
			LastCheckinDate:   time.Date(1970, 1, 1, 0, 0, 0, 0, time.UTC),
		}

		if err := s.childPointsRepo.Create(childPoints); err != nil {
			logger.Error("Failed to create child points", "child_id", childID, "error", err)
			return errcode.ErrDatabase.WithDetails("创建孩子积分记录失败")
		}
	}

	// 更新积分
	newTotalPoints := childPoints.TotalPoints + int64(points)

	// 创建积分变动记录
	pointRecord := &models.PointRecords{
		ChildID:      int64(childID),
		SourceType:   sourceType,
		SourceID:     int64(sourceID),
		PointsChange: points,
		PointsAfter:  newTotalPoints,
		Description:  description,
	}

	if err := s.pointRecordsRepo.Create(pointRecord); err != nil {
		logger.Error("Failed to create point record", "child_id", childID, "error", err)
		return errcode.ErrDatabase.WithDetails("创建积分记录失败")
	}

	// 更新孩子积分统计
	childPoints.TotalPoints = newTotalPoints
	childPoints.WeekPoints += points
	childPoints.MonthPoints += points

	if err := s.childPointsRepo.Update(childPoints.ID, childPoints); err != nil {
		logger.Error("Failed to update child points", "child_id", childID, "error", err)
		return errcode.ErrDatabase.WithDetails("更新孩子积分失败")
	}

	logger.Info("Points added successfully", "child_id", childID, "points", points, "total", newTotalPoints)
	return nil
}

// GetPointsStats 获取积分统计
func (s *pointsService) GetPointsStats(childID uint) (*PointsStatsResponse, error) {
	childPoints, err := s.childPointsRepo.GetByChildID(childID)
	if err != nil {
		if err == errcode.ErrDataNotFound {
			// 返回默认的空统计
			return &PointsStatsResponse{
				TotalPoints:       0,
				WeekPoints:        0,
				MonthPoints:       0,
				TotalCheckins:     0,
				ContinuousDays:    0,
				MaxContinuousDays: 0,
				WeekRank:          0,
				LastWeekRank:      0,
				LastCheckinDate:   time.Date(1970, 1, 1, 0, 0, 0, 0, time.UTC),
				CurrentLevel:      1,
				NextLevelPoints:   100,
			}, nil
		}
		logger.Error("Failed to get child points", "child_id", childID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取积分统计失败")
	}

	// 计算当前等级和下一等级所需积分
	currentLevel := s.calculateLevel(childPoints.TotalPoints)
	nextLevelPoints := s.calculateNextLevelPoints(currentLevel)

	stats := &PointsStatsResponse{
		TotalPoints:       childPoints.TotalPoints,
		WeekPoints:        childPoints.WeekPoints,
		MonthPoints:       childPoints.MonthPoints,
		TotalCheckins:     childPoints.TotalCheckins,
		ContinuousDays:    childPoints.ContinuousDays,
		MaxContinuousDays: childPoints.MaxContinuousDays,
		WeekRank:          childPoints.WeekRank,
		LastWeekRank:      childPoints.LastWeekRank,
		LastCheckinDate:   childPoints.LastCheckinDate,
		CurrentLevel:      currentLevel,
		NextLevelPoints:   nextLevelPoints,
	}

	logger.Info("Points stats retrieved successfully", "child_id", childID, "total_points", stats.TotalPoints)
	return stats, nil
}

// GetPointsHistory 获取积分历史
func (s *pointsService) GetPointsHistory(childID uint, offset, limit int) (*PointsHistoryResponse, error) {
	records, total, err := s.pointRecordsRepo.GetByChildID(childID, offset, limit)
	if err != nil {
		logger.Error("Failed to get points history", "child_id", childID, "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取积分历史失败")
	}

	var responseList []*models.PointRecordsResponse
	for _, record := range records {
		responseList = append(responseList, record.ToResponse())
	}

	logger.Info("Points history retrieved successfully", "child_id", childID, "total", total)
	return &PointsHistoryResponse{
		List:  responseList,
		Total: total,
	}, nil
}

// GetLeaderboard 获取排行榜
func (s *pointsService) GetLeaderboard(limit int) (*LeaderboardResponse, error) {
	// 获取周排行榜
	weeklyRanking, err := s.childPointsRepo.GetWeeklyRanking(limit)
	if err != nil {
		logger.Error("Failed to get weekly ranking", "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取周排行榜失败")
	}

	// 获取月排行榜
	monthlyRanking, err := s.childPointsRepo.GetMonthlyRanking(limit)
	if err != nil {
		logger.Error("Failed to get monthly ranking", "error", err)
		return nil, errcode.ErrDatabase.WithDetails("获取月排行榜失败")
	}

	// 转换为响应格式
	weeklyItems := s.convertToLeaderboardItems(weeklyRanking)
	monthlyItems := s.convertToLeaderboardItems(monthlyRanking)

	return &LeaderboardResponse{
		WeeklyRanking:  weeklyItems,
		MonthlyRanking: monthlyItems,
	}, nil
}

// UpdateWeeklyRanking 更新周排名
func (s *pointsService) UpdateWeeklyRanking() error {
	// 获取所有孩子的积分记录，按周积分排序
	allChildPoints, err := s.childPointsRepo.GetAllOrderByWeekPoints()
	if err != nil {
		logger.Error("Failed to get all child points for ranking", "error", err)
		return errcode.ErrDatabase.WithDetails("获取积分记录失败")
	}

	// 更新排名
	for i, childPoints := range allChildPoints {
		childPoints.LastWeekRank = childPoints.WeekRank
		childPoints.WeekRank = i + 1

		if err := s.childPointsRepo.Update(childPoints.ID, childPoints); err != nil {
			logger.Error("Failed to update child ranking", "child_id", childPoints.ChildID, "error", err)
			// 继续处理其他记录
		}
	}

	logger.Info("Weekly ranking updated successfully", "total_children", len(allChildPoints))
	return nil
}

// ==================== 辅助方法 ====================

// calculateLevel 根据总积分计算等级
func (s *pointsService) calculateLevel(totalPoints int64) int {
	// 等级计算规则：每100积分一个等级
	level := int(totalPoints/100) + 1
	if level > 100 {
		level = 100 // 最高等级100
	}
	return level
}

// calculateNextLevelPoints 计算下一等级所需积分
func (s *pointsService) calculateNextLevelPoints(currentLevel int) int64 {
	if currentLevel >= 100 {
		return 0 // 已达到最高等级
	}
	return int64(currentLevel * 100)
}

// convertToLeaderboardItems 转换为排行榜项目
func (s *pointsService) convertToLeaderboardItems(childPoints []*models.ChildPoints) []*LeaderboardItem {
	var items []*LeaderboardItem

	for i, cp := range childPoints {
		item := &LeaderboardItem{
			ChildID:        uint(cp.ChildID),
			ChildName:      "孩子", // 这里需要关联children表获取真实姓名
			Points:         cp.TotalPoints,
			Rank:           i + 1,
			ContinuousDays: cp.ContinuousDays,
		}
		items = append(items, item)
	}

	return items
}
