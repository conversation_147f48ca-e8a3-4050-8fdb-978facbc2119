package api

import (
	"strconv"

	"kids-platform/internal/services/api"
	"kids-platform/pkg/logger"
	"kids-platform/pkg/response"

	"github.com/gin-gonic/gin"
)

// PointsHandler 积分相关处理器
type PointsHandler struct {
	pointsService api.PointsService
}

// NewPointsHandler 创建积分处理器
func NewPointsHandler(pointsService api.PointsService) *PointsHandler {
	return &PointsHandler{
		pointsService: pointsService,
	}
}

// GetPointsStats 获取积分统计
// @Summary 获取积分统计
// @Description 获取用户的积分统计信息
// @Tags 积分管理
// @Accept json
// @Produce json
// @Param child_id query int true "孩子ID"
// @Success 200 {object} response.Response{data=api.PointsStatsResponse}
// @Router /user/points [get]
func (h *PointsHandler) GetPointsStats(c *gin.Context) {
	// 获取用户信息
	userID := h.getUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 获取孩子ID
	childIDStr := c.Query("child_id")
	if childIDStr == "" {
		response.BadRequest(c, "请提供孩子ID")
		return
	}

	childID, err := strconv.ParseUint(childIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的孩子ID")
		return
	}

	// 调用服务层
	result, err := h.pointsService.GetPointsStats(uint(childID))
	if err != nil {
		logger.Error("Failed to get points stats", "user_id", userID, "child_id", childID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// GetPointsHistory 获取积分历史
// @Summary 获取积分历史
// @Description 获取用户的积分变动历史
// @Tags 积分管理
// @Accept json
// @Produce json
// @Param child_id query int true "孩子ID"
// @Param page query int false "页码" default(1)
// @Param limit query int false "每页数量" default(10)
// @Success 200 {object} response.Response{data=api.PointsHistoryResponse}
// @Router /user/points/history [get]
func (h *PointsHandler) GetPointsHistory(c *gin.Context) {
	// 获取用户信息
	userID := h.getUserID(c)
	if userID == 0 {
		response.Unauthorized(c, "请先登录")
		return
	}

	// 获取孩子ID
	childIDStr := c.Query("child_id")
	if childIDStr == "" {
		response.BadRequest(c, "请提供孩子ID")
		return
	}

	childID, err := strconv.ParseUint(childIDStr, 10, 32)
	if err != nil {
		response.BadRequest(c, "无效的孩子ID")
		return
	}

	// 获取分页参数
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))

	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}

	offset := (page - 1) * limit

	// 调用服务层
	result, err := h.pointsService.GetPointsHistory(uint(childID), offset, limit)
	if err != nil {
		logger.Error("Failed to get points history", "user_id", userID, "child_id", childID, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// GetLeaderboard 获取排行榜
// @Summary 获取排行榜
// @Description 获取积分排行榜
// @Tags 积分管理
// @Accept json
// @Produce json
// @Param limit query int false "排行榜数量" default(20)
// @Success 200 {object} response.Response{data=api.LeaderboardResponse}
// @Router /leaderboard [get]
func (h *PointsHandler) GetLeaderboard(c *gin.Context) {
	// 获取排行榜数量参数
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "20"))

	if limit < 1 || limit > 100 {
		limit = 20
	}

	// 调用服务层
	result, err := h.pointsService.GetLeaderboard(limit)
	if err != nil {
		logger.Error("Failed to get leaderboard", "limit", limit, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, result)
}

// UpdateWeeklyRanking 更新周排名
// @Summary 更新周排名
// @Description 更新周排名（管理员接口）
// @Tags 积分管理
// @Accept json
// @Produce json
// @Success 200 {object} response.Response
// @Router /admin/points/update-ranking [post]
func (h *PointsHandler) UpdateWeeklyRanking(c *gin.Context) {
	// TODO: 添加管理员权限验证

	// 调用服务层
	err := h.pointsService.UpdateWeeklyRanking()
	if err != nil {
		logger.Error("Failed to update weekly ranking", "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, gin.H{"message": "周排名更新成功"})
}

// AddPoints 添加积分（管理员接口）
// @Summary 添加积分
// @Description 为用户添加积分（管理员接口）
// @Tags 积分管理
// @Accept json
// @Produce json
// @Param request body AddPointsRequest true "添加积分请求"
// @Success 200 {object} response.Response
// @Router /admin/points/add [post]
func (h *PointsHandler) AddPoints(c *gin.Context) {
	var req AddPointsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		response.BadRequest(c, "请求参数错误: "+err.Error())
		return
	}

	// TODO: 添加管理员权限验证

	// 调用服务层
	err := h.pointsService.AddPoints(req.ChildID, req.SourceType, req.SourceID, req.Points, req.Description)
	if err != nil {
		logger.Error("Failed to add points", "child_id", req.ChildID, "points", req.Points, "error", err)
		response.ErrorWithCode(c, err)
		return
	}

	response.Success(c, gin.H{"message": "积分添加成功"})
}

// ==================== 请求结构定义 ====================

// AddPointsRequest 添加积分请求
type AddPointsRequest struct {
	ChildID     uint   `json:"child_id" binding:"required"`    // 孩子ID
	SourceType  int8   `json:"source_type" binding:"required"` // 来源类型
	SourceID    uint   `json:"source_id" binding:"required"`   // 来源ID
	Points      int    `json:"points" binding:"required"`      // 积分数量
	Description string `json:"description" binding:"required"` // 描述
}

// ==================== 辅助方法 ====================

// getUserID 从上下文获取用户ID
func (h *PointsHandler) getUserID(c *gin.Context) uint {
	if userID, exists := c.Get("user_id"); exists {
		// JWT中间件设置的是uint64类型，需要正确处理
		if id, ok := userID.(uint64); ok {
			return uint(id)
		}
	}
	return 0
}
