// 成长页面 - 学习行动中心 - 使用状态管理系统和登录守卫
const {
  pageUtils,
  constants,
  wechat,
  dataUtils,
  childrenActions,
  businessActions,
  createStatePage,
  withLoginGuardPreset,
  startLoading,
  endLoading,
  isLoading,
} = require("../../utils/index.js");

const contentAPI = require("../../apis/content.js");
const app = getApp();

// 使用状态管理和登录守卫创建页面
const growthPage = withLoginGuardPreset("basic")(
  createStatePage({
    data: {
      navbarHeight: 44, // 默认导航栏高度
      currentTab: 0, // 当前Tab：0-任务，1-成长
      // 状态管理会自动绑定这些数据
      currentChild: null,
      activeTasks: [],
      recentRecords: [],
      mainProgress: {
        current: 0,
        total: 21,
        percentage: 0,
        remaining: 21,
      },
      // 成长Tab数据
      userStats: {
        totalPoints: 2580,
        currentLevel: 8,
        streakDays: 15,
        completedContracts: 3,
      },
      medalsList: [],
      growthTrack: [],
    },

    onLoad() {
      console.log("成长页面加载");

      // 绑定状态到页面数据
      this.bindChildrenState();
      this.bindBusinessState();

      this.initPage();
    },

    // 登录状态变化回调
    onLoginChange(isLoggedIn, userInfo) {
      console.log("成长页面 - 登录状态变化:", isLoggedIn);
      if (isLoggedIn) {
        // 登录成功后重新加载数据
        this.loadActiveTasks();
      } else {
        // 未登录时显示引导信息
        this.showLoginPrompt();
      }
    },

    onShow() {
      console.log("成长页面显示");
      this.refreshData();
    },

    // 状态变化回调
    onStateChange(path, newValue, oldValue) {
      console.log(`成长页面状态变化: ${path}`, newValue);

      // 当前孩子变化时重新加载数据
      if (path === "children.currentChild" && newValue !== oldValue) {
        this.loadActiveTasks();
      }

      // 活跃任务变化时更新进度
      if (path === "business.activeTasks") {
        this.updateMainProgress();
      }
    },

    // 初始化页面
    initPage() {
      this.loadActiveTasks();
      this.loadGrowthData();
    },

    // 刷新数据
    refreshData() {
      this.loadActiveTasks();
      this.loadGrowthData();
    },

    // Tab切换
    switchTab(e) {
      const tab = parseInt(e.currentTarget.dataset.tab);
      this.setData({
        currentTab: tab,
      });
    },

    // 导航栏高度变化事件
    onNavbarHeightChange(e) {
      this.setData({
        navbarHeight: e.detail.navbarHeight,
      });
    },

    // 孩子切换事件
    onChildChange(e) {
      console.log("孩子切换:", e.detail.child);
      // 使用状态管理器更新当前孩子
      childrenActions.setCurrentChild(e.detail.child);
      // 状态变化会自动触发数据重新加载
    },

    // 加载活跃任务
    async loadActiveTasks() {
      const currentChild = childrenActions.getCurrentChild();
      if (!currentChild || !currentChild.id) {
        // 使用状态管理器更新业务数据
        businessActions.setActiveTasks([]);
        businessActions.setRecentRecords([]);
        this.updateMainProgress();
        return;
      }

      const loadingKey = "load-active-tasks";

      try {
        if (!isLoading(loadingKey)) {
          startLoading(loadingKey, {
            title: "加载训练营数据...",
            timeout: 10000,
          });
        }

        console.log("🏕️ 开始加载用户训练营列表，孩子ID:", currentChild.id);

        // 调用API获取用户参与的训练营列表
        const response = await contentAPI.getUserCamps(currentChild.id);

        console.log("✅ 用户训练营列表加载成功:", response);

        let activeTasks = [];

        if (response && response.data && Array.isArray(response.data)) {
          // 转换API数据为页面需要的格式
          activeTasks = response.data.map((camp) => ({
            id: camp.camp_id,
            camp_id: camp.camp_id,
            campTitle: `🌐 ${camp.title}`,
            checkin_count: camp.total_checkins || 0,
            totalCount: camp.duration_days || 21,
            todayTask:
              camp.today_status === "completed" ? "今日已完成" : "待完成训练",
            status: camp.participation_status === 1 ? "active" : "completed",
            progressPercentage: camp.progress_percentage || 0,
            consecutiveDays: camp.consecutive_days || 0,
            totalStudyMinutes: camp.total_study_minutes || 0,
            dataSource: "🌐 API数据",
          }));
        }

        // 如果没有数据，使用模拟数据作为后备
        if (activeTasks.length === 0) {
          console.log("📦 使用模拟数据作为后备");
          activeTasks = [
            {
              id: 1,
              camp_id: 1,
              campTitle: "📦 [模拟] 《零基础21天跳绳挑战营》",
              checkin_count: 8,
              totalCount: 21,
              todayTask: "连续跳绳50个",
              status: "active",
              dataSource: "📦 本地模拟数据",
            },
          ];
        }

        // 计算主要进度（取第一个任务）
        const mainTask = activeTasks[0];
        const mainProgress = {
          current: mainTask.checkin_count,
          total: mainTask.totalCount,
          percentage: Math.round(
            (mainTask.checkin_count / mainTask.totalCount) * 100
          ),
          remaining: mainTask.totalCount - mainTask.checkin_count,
        };

        // 使用状态管理器更新数据
        businessActions.setActiveTasks(activeTasks);
        businessActions.setRecentRecords(
          this.generateRecentRecords(mainTask.checkin_count)
        );

        // 更新主进度
        this.setData({
          mainProgress: mainProgress,
        });

        endLoading(loadingKey, true);
      } catch (error) {
        console.error("❌ 加载用户训练营列表失败:", error);
        endLoading(loadingKey, false);

        // 使用模拟数据作为后备
        console.log("📦 使用模拟数据作为后备");
        const activeTasks = [
          {
            id: 1,
            camp_id: 1,
            campTitle: "⚠️ [API失败-模拟] 《零基础21天跳绳挑战营》",
            checkin_count: 8,
            totalCount: 21,
            todayTask: "连续跳绳50个",
            status: "active",
            dataSource: "⚠️ API失败-模拟数据",
          },
        ];

        const mainTask = activeTasks[0];
        const mainProgress = {
          current: mainTask.checkin_count,
          total: mainTask.totalCount,
          percentage: Math.round(
            (mainTask.checkin_count / mainTask.totalCount) * 100
          ),
          remaining: mainTask.totalCount - mainTask.checkin_count,
        };

        businessActions.setActiveTasks(activeTasks);
        businessActions.setRecentRecords(
          this.generateRecentRecords(mainTask.checkin_count)
        );

        this.setData({
          mainProgress: mainProgress,
        });
      }
    },

    // 更新主进度
    updateMainProgress() {
      const activeTasks = businessActions.getActiveTasks();
      if (activeTasks && activeTasks.length > 0) {
        const mainTask = activeTasks[0];
        const mainProgress = {
          current: mainTask.checkin_count,
          total: mainTask.totalCount,
          percentage: Math.round(
            (mainTask.checkin_count / mainTask.totalCount) * 100
          ),
          remaining: mainTask.totalCount - mainTask.checkin_count,
        };

        this.setData({
          mainProgress: mainProgress,
        });
      }
    },

    // 生成最近打卡记录
    generateRecentRecords(checkinCount) {
      const records = [];
      for (let i = Math.max(1, checkinCount - 6); i <= checkinCount + 7; i++) {
        records.push({
          count: i,
          status: i <= checkinCount ? "completed" : "pending",
        });
      }
      return records;
    },

    // 加载成长数据
    loadGrowthData() {
      const currentChild = childrenActions.getCurrentChild();
      if (!currentChild || !currentChild.id) {
        this.setData({
          medalsList: [],
          growthTrack: [],
        });
        return;
      }

      // 模拟勋章数据
      const medalsList = [
        {
          id: 1,
          name: "运动达人",
          icon: "🏃‍♂️",
          unlocked: true,
          progress: 30,
          target: 30,
        },
        {
          id: 2,
          name: "阅读之星",
          icon: "📚",
          unlocked: true,
          progress: 50,
          target: 50,
        },
        {
          id: 3,
          name: "连续打卡7天",
          icon: "⚡",
          unlocked: true,
          progress: 7,
          target: 7,
        },
        {
          id: 4,
          name: "连续打卡21天",
          icon: "🔥",
          unlocked: false,
          progress: 15,
          target: 21,
        },
      ];

      // 模拟成长轨迹数据
      const growthTrack = [
        {
          id: 1,
          title: "完成阅读打卡",
          description: "阅读《小王子》30分钟，记录读后感",
          details:
            "今天完成了《小王子》第3章的阅读，深入理解了小王子的内心世界",
          icon: "📚",
        },
        {
          id: 2,
          title: "跳绳训练突破",
          description: "连续跳绳达到新纪录",
          details: "今天跳绳训练中突破了个人最好成绩，连续跳了150个",
          icon: "🏃‍♂️",
        },
        {
          id: 3,
          title: "获得新勋章",
          description: "解锁运动达人勋章",
          details: "通过坚持不懈的努力，成功解锁了运动达人勋章",
          icon: "🏅",
        },
      ];

      this.setData({
        medalsList: medalsList,
        growthTrack: growthTrack,
      });
    },

    // 跳转到打卡页面
    goToCheckin(e) {
      const task = e.currentTarget.dataset.task;
      wx.navigateTo({
        url: `/pages/checkin/checkin?taskId=${task.id}&campId=${task.camp_id}`,
      });
    },

    // 跳转到视频列表
    goToVideoList(e) {
      const task = e.currentTarget.dataset.task;
      wx.navigateTo({
        url: `/pages/video-list/video-list?campId=${task.camp_id}&type=course`,
      });
    },

    // 跳转到数据详情
    goToDataDetail() {
      wx.navigateTo({
        url: "/pages/data-detail/data-detail",
      });
    },

    // 跳转到全部记录
    goToAllRecords() {
      wx.navigateTo({
        url: "/pages/checkin-records/checkin-records",
      });
    },

    // 跳转到数据统计
    goToDataStats() {
      wx.navigateTo({
        url: "/pages/data-stats/data-stats",
      });
    },

    // 跳转到排行榜
    goToLeaderboard() {
      wx.navigateTo({
        url: "/pages/leaderboard/leaderboard",
      });
    },

    // 跳转到邀请推广
    goToInvite() {
      wx.navigateTo({
        url: "/pages/invite/invite",
      });
    },

    // 跳转到首页
    goToHome() {
      wx.switchTab({
        url: "/pages/home/<USER>",
      });
    },

    // 下拉刷新
    onPullDownRefresh() {
      this.refreshData();
      setTimeout(() => {
        wx.stopPullDownRefresh();
      }, 1000);
    },

    // 分享
    onShareAppMessage() {
      const child = this.data.currentChild;
      if (child.name && this.data.activeTasks.length > 0) {
        const task = this.data.activeTasks[0];
        return {
          title: `${child.name}已经坚持打卡${task.checkin_count}次了！一起来跳绳吧`,
          path: "/pages/home/<USER>",
          image_url: "/images/share-growth.png",
        };
      }

      return {
        title: "跳跳星球 - 让孩子爱上运动",
        path: "/pages/home/<USER>",
        image_url: "/images/share-growth.png",
      };
    },

    onShareTimeline() {
      return {
        title: "跳跳星球 - 让孩子爱上运动",
        image_url: "/images/share-growth.png",
      };
    },

    // 显示登录提示
    showLoginPrompt() {
      // 可以显示一些引导信息，但不强制要求登录
      console.log("用户未登录，显示基础内容");
    },

    // 手动触发登录检查
    async checkLogin() {
      const hasLogin = await this.ensureLogin({
        required: false,
        silent: false,
      });

      if (hasLogin) {
        pageUtils.showSuccess("登录成功");
        this.loadActiveTasks();
      }
    },

    // 查看勋章详情
    viewMedalDetail(e) {
      const medal = e.currentTarget.dataset.medal;
      wx.showModal({
        title: medal.name,
        content: medal.unlocked
          ? `恭喜获得${medal.name}勋章！`
          : `还需要完成 ${medal.target - medal.progress} 次才能获得此勋章`,
        showCancel: false,
      });
    },

    // 分享勋章墙
    shareMedals() {
      wx.navigateTo({
        url: "/pages/social/share-poster/share-poster?type=medals",
      });
    },

    // 分享单个勋章
    shareSingleMedal(e) {
      const medal = e.currentTarget.dataset.medal;
      wx.navigateTo({
        url: `/pages/social/share-poster/share-poster?type=medal&medalId=${medal.id}`,
      });
    },

    // 分享成长记录
    shareGrowthRecord() {
      wx.navigateTo({
        url: "/pages/social/share-poster/share-poster?type=growth",
      });
    },

    // 分享成长卡片
    shareGrowthCard(e) {
      const card = e.currentTarget.dataset.card;
      wx.navigateTo({
        url: `/pages/social/share-poster/share-poster?type=card&cardId=${card.id}`,
      });
    },
  })
);

// 创建页面实例
Page(growthPage);
