/**
 * 测试checkin API修复后的功能
 * 验证前后端参数传递是否正常
 */

const checkinAPI = require('./apis/checkin.js');

// 模拟测试数据
const testData = {
  childId: 123,
  campId: 456,
  checkinData: {
    child_id: 123,
    camp_id: 456,
    practice_duration: 15,
    jump_count_1min: 100,
    jump_count_continuous: 50,
    feeling_text: "今天跳得很好",
    feeling_score: 8
  },
  historyParams: {
    child_id: 123,
    camp_id: 456,
    page: 1,
    limit: 10
  }
};

/**
 * 测试获取打卡历史（修复变量名错误）
 */
async function testGetCheckinHistory() {
  console.log('\n🧪 测试获取打卡历史...');
  
  try {
    // 这个调用之前会报错：ReferenceError: checkinData is not defined
    // 修复后应该正常工作
    const result = await checkinAPI.getCheckinHistory(testData.historyParams);
    console.log('✅ 获取打卡历史成功:', result);
    return true;
  } catch (error) {
    console.error('❌ 获取打卡历史失败:', error.message);
    return false;
  }
}

/**
 * 测试获取今日打卡状态（修复参数传递）
 */
async function testGetTodayCheckinStatus() {
  console.log('\n🧪 测试获取今日打卡状态...');
  
  try {
    // 这个调用之前会报400错误：child_id和camp_id参数验证失败
    // 修复后应该正确传递query参数
    const result = await checkinAPI.getTodayCheckinStatus(testData.childId, testData.campId);
    console.log('✅ 获取今日打卡状态成功:', result);
    return true;
  } catch (error) {
    console.error('❌ 获取今日打卡状态失败:', error.message);
    return false;
  }
}

/**
 * 测试获取打卡统计（修复参数传递）
 */
async function testGetCheckinStats() {
  console.log('\n🧪 测试获取打卡统计...');
  
  try {
    // 这个调用之前会报400错误：child_id和camp_id参数验证失败
    // 修复后应该正确传递query参数
    const result = await checkinAPI.getCheckinStats(testData.childId, testData.campId);
    console.log('✅ 获取打卡统计成功:', result);
    return true;
  } catch (error) {
    console.error('❌ 获取打卡统计失败:', error.message);
    return false;
  }
}

/**
 * 测试创建打卡记录（验证POST请求仍然正常）
 */
async function testCreateCheckin() {
  console.log('\n🧪 测试创建打卡记录...');
  
  try {
    // POST请求应该不受影响，仍然正常工作
    const result = await checkinAPI.createCheckin(testData.checkinData);
    console.log('✅ 创建打卡记录成功:', result);
    return true;
  } catch (error) {
    console.error('❌ 创建打卡记录失败:', error.message);
    return false;
  }
}

/**
 * 运行所有测试
 */
async function runAllTests() {
  console.log('🚀 开始测试checkin API修复...');
  console.log('=====================================');
  
  const tests = [
    { name: '获取打卡历史', fn: testGetCheckinHistory },
    { name: '获取今日打卡状态', fn: testGetTodayCheckinStatus },
    { name: '获取打卡统计', fn: testGetCheckinStats },
    { name: '创建打卡记录', fn: testCreateCheckin }
  ];
  
  let passedCount = 0;
  let totalCount = tests.length;
  
  for (const test of tests) {
    const passed = await test.fn();
    if (passed) {
      passedCount++;
    }
    
    // 等待一下，避免请求过快
    await new Promise(resolve => setTimeout(resolve, 1000));
  }
  
  console.log('\n📊 测试结果汇总:');
  console.log('=====================================');
  console.log(`✅ 通过: ${passedCount}/${totalCount}`);
  console.log(`❌ 失败: ${totalCount - passedCount}/${totalCount}`);
  
  if (passedCount === totalCount) {
    console.log('🎉 所有测试通过！checkin API修复成功！');
  } else {
    console.log('⚠️  部分测试失败，请检查修复是否完整。');
  }
}

/**
 * 测试URL参数构建（单独测试前端修复）
 */
function testUrlParameterBuilding() {
  console.log('\n🧪 测试URL参数构建...');
  
  // 模拟http.get方法的参数处理逻辑
  function buildUrl(baseUrl, config = {}) {
    let url = baseUrl;
    
    if (config.params) {
      const queryString = Object.keys(config.params)
        .filter(key => config.params[key] !== undefined && config.params[key] !== null)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(config.params[key])}`)
        .join('&');
      
      if (queryString) {
        url = url + (url.includes('?') ? '&' : '?') + queryString;
      }
    }
    
    return url;
  }
  
  // 测试用例
  const testCases = [
    {
      name: '基本参数',
      baseUrl: '/api/v1/checkins/today',
      config: { params: { child_id: 123, camp_id: 456 } },
      expected: '/api/v1/checkins/today?child_id=123&camp_id=456'
    },
    {
      name: '包含特殊字符',
      baseUrl: '/api/v1/checkins/stats',
      config: { params: { child_id: 123, camp_id: 456, period: 'month' } },
      expected: '/api/v1/checkins/stats?child_id=123&camp_id=456&period=month'
    },
    {
      name: '过滤空值',
      baseUrl: '/api/v1/checkins/history',
      config: { params: { child_id: 123, camp_id: 456, page: null, limit: undefined } },
      expected: '/api/v1/checkins/history?child_id=123&camp_id=456'
    }
  ];
  
  let allPassed = true;
  
  testCases.forEach(testCase => {
    const result = buildUrl(testCase.baseUrl, testCase.config);
    const passed = result === testCase.expected;
    
    console.log(`${passed ? '✅' : '❌'} ${testCase.name}:`);
    console.log(`   期望: ${testCase.expected}`);
    console.log(`   实际: ${result}`);
    
    if (!passed) {
      allPassed = false;
    }
  });
  
  if (allPassed) {
    console.log('✅ URL参数构建测试全部通过！');
  } else {
    console.log('❌ URL参数构建测试有失败项！');
  }
  
  return allPassed;
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  // 先测试URL参数构建
  testUrlParameterBuilding();
  
  // 再测试API调用（需要后端服务运行）
  // runAllTests();
}

module.exports = {
  testGetCheckinHistory,
  testGetTodayCheckinStatus,
  testGetCheckinStats,
  testCreateCheckin,
  testUrlParameterBuilding,
  runAllTests
};
