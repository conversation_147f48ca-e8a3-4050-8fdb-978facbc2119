/* 成长页面样式 */

.page-container {
  background-color: var(--background-color);
  min-height: 100vh;
}

/* Tab导航 */
.tab-nav {
  display: flex;
  background-color: #FFFFFF;
  border-bottom: 2rpx solid #E5E5E5;
  position: sticky;
  top: 0;
  z-index: 100;
}

.tab-item {
  flex: 1;
  text-align: center;
  padding: 32rpx 0;
  position: relative;
}

.tab-item.active {
  color: var(--primary-color);
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 4rpx;
  background-color: var(--primary-color);
  border-radius: 2rpx;
}

.tab-text {
  font-size: var(--font-lg);
  font-weight: 500;
}

/* Tab内容 */
.tab-content {
  padding: var(--spacing-lg);
}

/* 有任务的内容区域 */
.content-with-tasks {
  padding: 0;
}

/* 任务列表区域 */
.tasks-section {
  margin-bottom: var(--spacing-lg);
}

.section-title {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.section-title .emoji {
  font-size: var(--font-lg);
  margin-right: var(--spacing-sm);
}

.section-title text:last-child {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
}

.task-card {
  background-color: #FFFFFF;
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.task-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.task-title {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
  flex: 1;
}

.task-progress {
  font-size: var(--font-sm);
  color: var(--primary-color);
  font-weight: 600;
  background-color: #FFF2E8;
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-sm);
}

.task-content {
  margin-bottom: var(--spacing-md);
}

.task-description {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.5;
}

.task-actions {
  display: flex;
  gap: var(--spacing-md);
}

/* 进度区域 */
.progress-section {
  background-color: #FFFFFF;
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.progress-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.progress-title {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
}

.progress-link {
  font-size: var(--font-sm);
  color: var(--info-color);
}

.progress-content {
  
}

.progress-bar-container {
  margin-bottom: var(--spacing-sm);
}

.progress-bar {
  width: 100%;
  height: 16rpx;
  background-color: #F0F0F0;
  border-radius: 8rpx;
  overflow: hidden;
  margin-bottom: var(--spacing-xs);
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--primary-color) 0%, #E6692D 100%);
  border-radius: 8rpx;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: var(--font-sm);
  font-weight: 600;
  color: var(--text-primary);
}

.progress-description {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.5;
}

/* 打卡记录区域 */
.checkin-records-section {
  background-color: #FFFFFF;
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-lg);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.records-title {
  font-size: var(--font-md);
  font-weight: 600;
  color: var(--text-primary);
}

.records-link {
  font-size: var(--font-sm);
  color: var(--info-color);
}

.records-scroll {
  white-space: nowrap;
}

.records-list {
  display: flex;
  gap: var(--spacing-md);
  padding-bottom: var(--spacing-sm);
}

.record-item {
  flex-shrink: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
  padding: var(--spacing-sm);
  border-radius: var(--radius-sm);
  min-width: 80rpx;
}

.record-item.completed {
  background-color: #F6FFED;
}

.record-item.pending {
  background-color: #FFF7E6;
}

.record-count {
  font-size: var(--font-xs);
  color: var(--text-secondary);
  font-weight: 500;
}

.record-status {
  font-size: var(--font-md);
}

/* 成长Tab样式 */

/* 个人荣誉区域 */
.honor-overview-section {
  margin-bottom: var(--spacing-xl);
}

.honor-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2rpx;
  background: linear-gradient(135deg, #FFF5F0 0%, #FFFFFF 100%);
  border-radius: 24rpx;
  padding: var(--spacing-xl);
  box-shadow: 0 8rpx 32rpx rgba(255, 122, 69, 0.12);
  border: 2rpx solid rgba(255, 122, 69, 0.1);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  position: relative;
}

.stat-item:not(:last-child)::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1rpx;
  background-color: rgba(255, 122, 69, 0.2);
}

.stat-value {
  font-size: 48rpx;
  font-weight: 700;
  color: var(--primary-color);
  line-height: 1;
}

.stat-label {
  font-size: var(--font-sm);
  color: var(--text-secondary);
  font-weight: 500;
}

/* 勋章墙区域 */
.medals-section {
  margin-bottom: var(--spacing-lg);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.share-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-xs);
  background-color: var(--primary-color);
  color: #FFFFFF;
  font-size: var(--font-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: 40rpx;
  border: none;
}

.share-icon {
  font-size: var(--font-md);
}

.medals-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: var(--spacing-lg);
}

.medal-item {
  background: linear-gradient(135deg, #FFF5F0 0%, #FFFFFF 100%);
  border: 3rpx solid var(--primary-color);
  border-radius: 24rpx;
  padding: var(--spacing-xl);
  position: relative;
  min-height: 200rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.medal-item.locked {
  background: linear-gradient(135deg, #F5F5F5 0%, #FFFFFF 100%);
  border-color: #E5E5E5;
  opacity: 0.7;
}

.medal-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-md);
  width: 100%;
}

.medal-icon {
  font-size: 100rpx;
  margin-bottom: var(--spacing-sm);
}

.medal-name {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  text-align: center;
  line-height: 1.3;
}

.medal-progress {
  margin-top: var(--spacing-sm);
  padding: var(--spacing-xs) var(--spacing-md);
  background-color: rgba(255, 122, 69, 0.1);
  border-radius: var(--radius-md);
}

.progress-text {
  font-size: var(--font-sm);
  color: var(--primary-color);
  font-weight: 500;
}

.share-medal-btn {
  position: absolute;
  bottom: var(--spacing-md);
  left: 50%;
  transform: translateX(-50%);
  background-color: var(--info-color);
  color: #FFFFFF;
  font-size: var(--font-sm);
  padding: var(--spacing-sm) var(--spacing-lg);
  border-radius: 40rpx;
  border: none;
  width: 120rpx;
}

/* 成长轨迹区域 */
.growth-track-section {
  margin-bottom: var(--spacing-lg);
}

.growth-cards {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.growth-card {
  background: linear-gradient(135deg, #F8FFFE 0%, #FFFFFF 100%);
  border: 2rpx solid #E8F4FD;
  border-radius: 24rpx;
  padding: var(--spacing-xl);
  position: relative;
  box-shadow: 0 8rpx 24rpx rgba(74, 144, 226, 0.08);
}

.card-content {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.card-header {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-sm);
}

.card-icon {
  font-size: 80rpx;
  width: 100rpx;
  text-align: center;
  flex-shrink: 0;
}

.card-title {
  font-size: var(--font-lg);
  font-weight: 600;
  color: var(--text-primary);
  line-height: 1.3;
}

.card-desc {
  font-size: var(--font-md);
  color: var(--text-secondary);
  line-height: 1.5;
  margin-left: 120rpx;
}

.card-details {
  margin-left: 120rpx;
  margin-top: var(--spacing-sm);
  font-size: var(--font-sm);
  color: var(--text-secondary);
  line-height: 1.4;
}

.share-card-btn {
  position: absolute;
  bottom: var(--spacing-lg);
  right: var(--spacing-lg);
  background-color: var(--info-color);
  color: #FFFFFF;
  font-size: var(--font-sm);
  padding: var(--spacing-sm) var(--spacing-xl);
  border-radius: 40rpx;
  border: none;
  min-width: 120rpx;
}

/* 快捷入口区域 */
.quick-actions-section {
  display: flex;
  justify-content: space-around;
  background-color: #FFFFFF;
  border-radius: var(--radius-md);
  padding: var(--spacing-lg);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
}

.quick-action {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md);
  border-radius: var(--radius-sm);
  transition: background-color 0.3s ease;
}

.quick-action:active {
  background-color: #F5F5F5;
}

.action-icon {
  font-size: var(--font-xl);
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #F7F8FA;
  border-radius: 50%;
}

.action-text {
  font-size: var(--font-sm);
  color: var(--text-primary);
  font-weight: 500;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 60vh;
  padding: var(--spacing-xl);
}

.empty-illustration {
  width: 400rpx;
  height: 300rpx;
  margin-bottom: var(--spacing-xl);
}

.empty-illustration image {
  width: 100%;
  height: 100%;
}

.empty-content {
  text-align: center;
}

.empty-title {
  display: block;
  font-size: var(--font-xl);
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: var(--spacing-md);
}

.empty-subtitle {
  display: block;
  font-size: var(--font-md);
  color: var(--text-secondary);
  line-height: 1.6;
  margin-bottom: var(--spacing-xl);
}

/* 底部安全距离 */
.safe-area-bottom {
  height: calc(var(--spacing-xl) + env(safe-area-inset-bottom));
}


