<!-- 成长页面 - 学习行动中心 -->
<view class="page-container">
  <!-- 自定义导航栏 -->
  <custom-navbar title="成长" show-user-selector="{{true}}" show-back-button="{{false}}" bind:navbarHeightChange="onNavbarHeightChange" bind:childChange="onChildChange"></custom-navbar>
  <!-- Tab导航 -->
  <view class="tab-nav" style="margin-top: {{navbarHeight}}px;">
    <view class="tab-item {{currentTab === 0 ? 'active' : ''}}" bindtap="switchTab" data-tab="0">
      <text class="tab-text">任务</text>
    </view>
    <view class="tab-item {{currentTab === 1 ? 'active' : ''}}" bindtap="switchTab" data-tab="1">
      <text class="tab-text">成长</text>
    </view>
  </view>
  <!-- 任务Tab内容 -->
  <view class="tab-content" wx:if="{{currentTab === 0}}">
    <!-- 有训练营的状态 -->
    <view wx:if="{{activeTasks.length > 0}}" class="content-with-tasks">
      <!-- 任务列表区域 -->
      <view class="tasks-section">
        <view class="section-title">
          <text class="emoji">🎯</text>
          <text>进行中</text>
        </view>
        <view class="task-card" wx:for="{{activeTasks}}" wx:key="id">
          <view class="task-header">
            <text class="task-title">{{item.campTitle}}</text>
            <text class="task-progress">第{{item.checkinCount}}次打卡</text>
          </view>
          <view class="task-content">
            <text class="task-description">任务：{{item.todayTask}}</text>
          </view>
          <view class="task-actions">
            <button class="btn btn-primary btn-small" bindtap="goToCheckin" data-task="{{item}}">
              立即打卡
            </button>
            <button class="btn btn-outline btn-small" bindtap="goToVideoList" data-task="{{item}}">
              观看教学
            </button>
          </view>
        </view>
      </view>
      <!-- 进度区域 -->
      <view class="progress-section">
        <view class="progress-header" bindtap="goToDataDetail">
          <text class="progress-title">📊 我的进度</text>
          <text class="progress-link">查看详情 ></text>
        </view>
        <view class="progress-content">
          <view class="progress-bar-container">
            <view class="progress-bar">
              <view class="progress-fill" style="width: {{mainProgress.percentage}}%"></view>
            </view>
            <text class="progress-text">
              {{mainProgress.current}}/{{mainProgress.total}}次 ({{mainProgress.percentage}}%)
            </text>
          </view>
          <text class="progress-description">
            已打卡{{mainProgress.current}}次，再坚持{{mainProgress.remaining}}次就完成啦！
          </text>
        </view>
      </view>
      <!-- 打卡记录区域 -->
      <view class="checkin-records-section">
        <view class="records-header">
          <text class="records-title">📅 最近打卡记录</text>
          <text class="records-link" bindtap="goToAllRecords">查看全部 ></text>
        </view>
        <scroll-view class="records-scroll" scroll-x="true" show-scrollbar="{{false}}">
          <view class="records-list">
            <view class="record-item {{item.status}}" wx:for="{{recentRecords}}" wx:key="index">
              <text class="record-count">{{item.count}}次</text>
              <view class="record-status">
                <text wx:if="{{item.status === 'completed'}}">✅</text>
                <text wx:else>⭕</text>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>
      <!-- 快捷入口区域 -->
      <view class="quick-actions-section">
        <view class="quick-action" bindtap="goToDataStats">
          <view class="action-icon">
            <smart-image name="icon-statistics" custom-class="size-small"></smart-image>
          </view>
          <text class="action-text">数据统计</text>
        </view>
        <view class="quick-action" bindtap="goToLeaderboard">
          <view class="action-icon">
            <smart-image name="icon-trophy" custom-class="size-small"></smart-image>
          </view>
          <text class="action-text">排行榜</text>
        </view>
        <view class="quick-action" bindtap="goToInvite">
          <view class="action-icon">👥</view>
          <text class="action-text">邀请推广</text>
        </view>
      </view>
    </view>
    <!-- 无训练营的空状态 -->
    <view wx:else class="empty-state">
      <view class="empty-illustration">
        <view class="empty-state-emoji">📭</view>
      </view>
      <view class="empty-content">
        <text class="empty-title">🎯 还没有参与训练营</text>
        <text class="empty-subtitle">去首页选择适合的训练营开始学习吧！</text>
        <button class="btn btn-primary" bindtap="goToHome">去选择训练营</button>
      </view>
    </view>
  </view>
  <!-- 成长Tab内容 -->
  <view class="tab-content" wx:if="{{currentTab === 1}}">
    <!-- 个人荣誉总览 -->
    <view class="honor-overview-section">
      <view class="section-title">
        <text class="emoji">🌟</text>
        <text>个人荣誉</text>
      </view>
      <view class="honor-stats">
        <view class="stat-item">
          <text class="stat-value">{{userStats.totalPoints}}</text>
          <text class="stat-label">总积分</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{userStats.currentLevel}}</text>
          <text class="stat-label">当前等级</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{userStats.streakDays}}</text>
          <text class="stat-label">最长连续</text>
        </view>
        <view class="stat-item">
          <text class="stat-value">{{userStats.completedContracts}}</text>
          <text class="stat-label">完成契约</text>
        </view>
      </view>
    </view>
    <!-- 勋章墙 -->
    <view class="medals-section">
      <view class="section-header">
        <view class="section-title">
          <text class="emoji">🏅</text>
          <text>勋章墙</text>
        </view>
        <button class="share-btn" bindtap="shareMedals">
          <text class="share-icon">🔗</text>
          <text>分享勋章墙</text>
        </button>
      </view>
      <view class="medals-grid">
        <view class="medal-item {{item.unlocked ? 'unlocked' : 'locked'}}" wx:for="{{medalsList}}" wx:key="id" bindtap="viewMedalDetail" data-medal="{{item}}">
          <view class="medal-card">
            <view class="medal-icon">{{item.icon}}</view>
            <text class="medal-name">{{item.name}}</text>
            <view class="medal-progress" wx:if="{{!item.unlocked}}">
              <text class="progress-text">{{item.progress}}/{{item.target}}</text>
            </view>
          </view>
          <button class="share-medal-btn" wx:if="{{item.unlocked}}" bindtap="shareSingleMedal" data-medal="{{item}}" catchtap="true">
            分享
          </button>
        </view>
      </view>
    </view>
    <!-- 成长轨迹 -->
    <view class="growth-track-section">
      <view class="section-header">
        <view class="section-title">
          <text class="emoji">📈</text>
          <text>成长轨迹</text>
        </view>
        <button class="share-btn" bindtap="shareGrowthRecord">
          <text class="share-icon">🔗</text>
          <text>分享成长记录</text>
        </button>
      </view>
      <view class="growth-cards">
        <view class="growth-card" wx:for="{{growthTrack}}" wx:key="id">
          <view class="card-content">
            <view class="card-header">
              <view class="card-icon">{{item.icon}}</view>
              <text class="card-title">{{item.title}}</text>
            </view>
            <text class="card-desc">{{item.description}}</text>
            <text class="card-details" wx:if="{{item.details}}">{{item.details}}</text>
          </view>
          <button class="share-card-btn" bindtap="shareGrowthCard" data-card="{{item}}">分享</button>
        </view>
      </view>
    </view>
  </view>
  <!-- 底部安全距离 -->
  <view class="safe-area-bottom"></view>
</view>