package repositories

import (
	"kids-platform/internal/models"
	"kids-platform/pkg/errcode"

	"gorm.io/gorm"
)

// ChildPointsRepository 孩子积分统计表：统计每个孩子的积分和排行榜相关数据仓储接口
type ChildPointsRepository interface {
	Create(childPoints *models.ChildPoints) error
	GetByID(id uint) (*models.ChildPoints, error)
	Update(id uint, childPoints *models.ChildPoints) error
	Delete(id uint) error
	List(offset, limit int) ([]*models.ChildPoints, int64, error)
	// 根据孩子ID获取积分记录
	GetByChildID(childID uint) (*models.ChildPoints, error)
	// 获取周排行榜
	GetWeeklyRanking(limit int) ([]*models.ChildPoints, error)
	// 获取月排行榜
	GetMonthlyRanking(limit int) ([]*models.ChildPoints, error)
	// 获取所有记录按周积分排序
	GetAllOrderByWeekPoints() ([]*models.ChildPoints, error)
}

// childPointsRepository 孩子积分统计表：统计每个孩子的积分和排行榜相关数据仓储实现
type childPointsRepository struct {
	db *gorm.DB
}

// NewChildPointsRepository 创建孩子积分统计表：统计每个孩子的积分和排行榜相关数据仓储
func NewChildPointsRepository(db *gorm.DB) ChildPointsRepository {
	return &childPointsRepository{
		db: db,
	}
}

// Create 创建孩子积分统计表：统计每个孩子的积分和排行榜相关数据
func (r *childPointsRepository) Create(childPoints *models.ChildPoints) error {
	if err := r.db.Create(childPoints).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("创建孩子积分统计表：统计每个孩子的积分和排行榜相关数据失败")
	}
	return nil
}

// GetByID 根据ID获取孩子积分统计表：统计每个孩子的积分和排行榜相关数据
func (r *childPointsRepository) GetByID(id uint) (*models.ChildPoints, error) {
	var childPoints models.ChildPoints
	if err := r.db.First(&childPoints, id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("孩子积分统计表：统计每个孩子的积分和排行榜相关数据不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询孩子积分统计表：统计每个孩子的积分和排行榜相关数据失败")
	}
	return &childPoints, nil
}

// Update 更新孩子积分统计表：统计每个孩子的积分和排行榜相关数据
func (r *childPointsRepository) Update(id uint, childPoints *models.ChildPoints) error {
	if err := r.db.Model(&models.ChildPoints{}).Where("id = ?", id).Updates(childPoints).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("更新孩子积分统计表：统计每个孩子的积分和排行榜相关数据失败")
	}
	return nil
}

// Delete 删除孩子积分统计表：统计每个孩子的积分和排行榜相关数据
func (r *childPointsRepository) Delete(id uint) error {
	if err := r.db.Delete(&models.ChildPoints{}, id).Error; err != nil {
		return errcode.ErrDatabase.WithDetails("删除孩子积分统计表：统计每个孩子的积分和排行榜相关数据失败")
	}
	return nil
}

// List 获取孩子积分统计表：统计每个孩子的积分和排行榜相关数据列表
func (r *childPointsRepository) List(offset, limit int) ([]*models.ChildPoints, int64, error) {
	var childPointss []*models.ChildPoints
	var total int64

	// 获取总数
	if err := r.db.Model(&models.ChildPoints{}).Count(&total).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询孩子积分统计表：统计每个孩子的积分和排行榜相关数据总数失败")
	}

	// 获取列表
	if err := r.db.Offset(offset).Limit(limit).Find(&childPointss).Error; err != nil {
		return nil, 0, errcode.ErrDatabase.WithDetails("查询孩子积分统计表：统计每个孩子的积分和排行榜相关数据列表失败")
	}

	return childPointss, total, nil
}

// GetByChildID 根据孩子ID获取积分记录
func (r *childPointsRepository) GetByChildID(childID uint) (*models.ChildPoints, error) {
	var childPoints models.ChildPoints
	if err := r.db.Where("child_id = ?", childID).First(&childPoints).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, errcode.ErrDataNotFound.WithDetails("孩子积分记录不存在")
		}
		return nil, errcode.ErrDatabase.WithDetails("查询孩子积分记录失败")
	}
	return &childPoints, nil
}

// GetWeeklyRanking 获取周排行榜
func (r *childPointsRepository) GetWeeklyRanking(limit int) ([]*models.ChildPoints, error) {
	var childPoints []*models.ChildPoints
	if err := r.db.Order("week_points DESC").Limit(limit).Find(&childPoints).Error; err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询周排行榜失败")
	}
	return childPoints, nil
}

// GetMonthlyRanking 获取月排行榜
func (r *childPointsRepository) GetMonthlyRanking(limit int) ([]*models.ChildPoints, error) {
	var childPoints []*models.ChildPoints
	if err := r.db.Order("month_points DESC").Limit(limit).Find(&childPoints).Error; err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询月排行榜失败")
	}
	return childPoints, nil
}

// GetAllOrderByWeekPoints 获取所有记录按周积分排序
func (r *childPointsRepository) GetAllOrderByWeekPoints() ([]*models.ChildPoints, error) {
	var childPoints []*models.ChildPoints
	if err := r.db.Order("week_points DESC").Find(&childPoints).Error; err != nil {
		return nil, errcode.ErrDatabase.WithDetails("查询积分记录失败")
	}
	return childPoints, nil
}
